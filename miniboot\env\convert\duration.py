"""
时间间隔转换器

提供时间间隔字符串到数值的转换功能，重用 Mini-Boot schedule 模块的解析逻辑。
"""

import re
from typing import Any

from miniboot.errors import ConversionError
from .base import Converter


class StrToDurationConverter(Converter[float]):
    """字符串到时间间隔转换器

    将时间间隔字符串转换为秒数（浮点数）。
    重用 Mini-Boot schedule 模块中的时间解析逻辑，确保框架内一致性。

    支持格式：
    - "30s" -> 30.0
    - "5m" -> 300.0
    - "1h" -> 3600.0
    - "2d" -> 172800.0
    - "30" -> 30.0 (默认秒)

    支持的时间单位：
    - s, sec, second, seconds - 秒
    - m, min, minute, minutes - 分钟
    - h, hour, hours - 小时
    - d, day, days - 天
    """

    def can_convert(self, source_type: type, target_type: type) -> bool:
        """检查是否可以转换"""
        return source_type is str and target_type is float

    def convert(self, source: Any, _target_type: type[float]) -> float:
        """执行转换"""
        if not isinstance(source, str):
            raise ConversionError(f"Expected string, got {type(source).__name__}")

        # 检查是否是时间间隔格式
        if not self._is_duration_format(source):
            raise ConversionError(f"Not a duration format: {source}")

        return self._parse_duration(source)

    def _is_duration_format(self, value: str) -> bool:
        """检查字符串是否是时间间隔格式"""
        if not value:
            return False

        value_str = value.strip().lower()

        # 排除百分比格式
        if '%' in value_str:
            return False

        # 匹配时间格式：数字+时间单位
        pattern = r"^(\d+(?:\.\d+)?)\s*([a-z]*)$"
        match = re.match(pattern, value_str)

        if not match:
            return False

        _, unit = match.groups()
        valid_units = {"", "s", "sec", "second", "seconds", "m", "min", "minute", "minutes",
                      "h", "hour", "hours", "d", "day", "days"}

        return unit in valid_units

    def _parse_duration(self, duration: str) -> float:
        """解析时间间隔字符串为秒数

        重用 Mini-Boot schedule 模块的解析逻辑，确保一致性。

        Args:
            duration: 时间间隔字符串

        Returns:
            float: 秒数

        Raises:
            ConversionError: 格式不正确时
        """
        if not duration:
            raise ConversionError("时间间隔不能为空")

        # 匹配时间格式：数字+单位
        # 支持多种时间单位格式
        pattern = r"^(\d+(?:\.\d+)?)\s*([a-z]*)$"
        match = re.match(pattern, duration.lower().strip())

        if not match:
            raise ConversionError(f"Invalid duration format: {duration}")

        value, unit = match.groups()
        try:
            value = float(value)
        except ValueError as e:
            raise ConversionError(f"Invalid numeric value in duration: {duration}") from e

        # 转换为秒，支持多种单位格式
        # 与 schedule 模块保持一致
        multipliers = {
            "": 1,          # 默认秒
            "s": 1,         # 秒
            "sec": 1,       # 秒
            "second": 1,    # 秒
            "seconds": 1,   # 秒
            "m": 60,        # 分钟
            "min": 60,      # 分钟
            "minute": 60,   # 分钟
            "minutes": 60,  # 分钟
            "h": 3600,      # 小时
            "hour": 3600,   # 小时
            "hours": 3600,  # 小时
            "d": 86400,     # 天
            "day": 86400,   # 天
            "days": 86400,  # 天
        }

        if unit not in multipliers:
            raise ConversionError(f"Invalid time unit: {unit}")

        return value * multipliers[unit]


class StrToSizeConverter(Converter[int]):
    """字符串到大小转换器

    将大小字符串转换为字节数。
    支持 KB、MB、GB 等单位。
    """

    def can_convert(self, source_type: type, target_type: type) -> bool:
        """检查是否可以转换"""
        return source_type is str and target_type is int

    def convert(self, source: Any, _target_type: type[int]) -> int:
        """执行转换"""
        if not isinstance(source, str):
            raise ConversionError(f"Expected string, got {type(source).__name__}")

        # 检查是否是大小格式
        if not self._is_size_format(source):
            raise ConversionError(f"Not a size format: {source}")

        return self._parse_size(source)

    def _is_size_format(self, value: str) -> bool:
        """检查字符串是否是大小格式"""
        if not value:
            return False

        value_str = value.strip().lower()

        # 排除百分比格式和时间格式
        if '%' in value_str:
            return False

        # 匹配大小格式：数字+大小单位
        pattern = r"^(\d+(?:\.\d+)?)\s*([a-z]*)$"
        match = re.match(pattern, value_str)

        if not match:
            return False

        _, unit = match.groups()
        valid_units = {"", "b", "byte", "bytes", "k", "kb", "kilobyte", "kilobytes",
                      "m", "mb", "megabyte", "megabytes", "g", "gb", "gigabyte", "gigabytes"}

        return unit in valid_units

    def _parse_size(self, size: str) -> int:
        """解析大小字符串为字节数

        Args:
            size: 大小字符串，如 "1KB", "10MB", "1GB"

        Returns:
            int: 字节数

        Raises:
            ConversionError: 格式不正确时
        """
        if not size:
            raise ConversionError("大小不能为空")

        # 匹配大小格式：数字+单位
        pattern = r"^(\d+(?:\.\d+)?)\s*([a-z]*)$"
        match = re.match(pattern, size.lower().strip())

        if not match:
            raise ConversionError(f"Invalid size format: {size}")

        value, unit = match.groups()
        try:
            value = float(value)
        except ValueError as e:
            raise ConversionError(f"Invalid numeric value in size: {size}") from e

        # 转换为字节
        multipliers = {
            "": 1,                    # 默认字节
            "b": 1,                   # 字节
            "byte": 1,                # 字节
            "bytes": 1,               # 字节
            "k": 1024,                # KB
            "kb": 1024,               # KB
            "kilobyte": 1024,         # KB
            "kilobytes": 1024,        # KB
            "m": 1024 * 1024,         # MB
            "mb": 1024 * 1024,        # MB
            "megabyte": 1024 * 1024,  # MB
            "megabytes": 1024 * 1024, # MB
            "g": 1024 * 1024 * 1024,  # GB
            "gb": 1024 * 1024 * 1024, # GB
            "gigabyte": 1024 * 1024 * 1024,   # GB
            "gigabytes": 1024 * 1024 * 1024,  # GB
        }

        if unit not in multipliers:
            raise ConversionError(f"Invalid size unit: {unit}")

        return int(value * multipliers[unit])


class StrToPercentageConverter(Converter[float]):
    """字符串到百分比转换器

    将百分比字符串转换为 0-1 之间的浮点数。
    """

    def can_convert(self, source_type: type, target_type: type) -> bool:
        """检查是否可以转换"""
        return source_type is str and target_type is float

    def convert(self, source: Any, _target_type: type[float]) -> float:
        """执行转换"""
        if not isinstance(source, str):
            raise ConversionError(f"Expected string, got {type(source).__name__}")

        # 检查是否是百分比格式
        if not self._is_percentage_format(source):
            raise ConversionError(f"Not a percentage format: {source}")

        return self._parse_percentage(source)

    def _is_percentage_format(self, value: str) -> bool:
        """检查字符串是否是百分比格式"""
        if not value:
            return False

        value_str = value.strip()

        # 百分比格式：以 % 结尾，或者是 0-1 之间的小数
        if value_str.endswith('%'):
            return True

        # 检查是否是 0-1 之间的小数
        try:
            val = float(value_str)
            return 0 <= val <= 1
        except ValueError:
            return False

    def _parse_percentage(self, percentage: str) -> float:
        """解析百分比字符串

        Args:
            percentage: 百分比字符串，如 "50%", "0.5", "75.5%"

        Returns:
            float: 0-1 之间的浮点数

        Raises:
            ConversionError: 格式不正确时
        """
        if not percentage:
            raise ConversionError("百分比不能为空")

        value_str = percentage.strip()

        # 处理百分号
        if value_str.endswith('%'):
            value_str = value_str[:-1].strip()
            is_percentage = True
        else:
            is_percentage = False

        try:
            value = float(value_str)
        except ValueError as e:
            raise ConversionError(f"Invalid percentage value: {percentage}") from e

        # 转换为 0-1 范围
        if is_percentage:
            if value < 0 or value > 100:
                raise ConversionError(f"Percentage must be between 0-100: {percentage}")
            return value / 100.0
        else:
            if value < 0 or value > 1:
                raise ConversionError(f"Decimal percentage must be between 0-1: {percentage}")
            return value


class StrToFloatCompositeConverter(Converter):
    """复合字符串到浮点数转换器

    智能处理多种格式：时间间隔、百分比、普通数字
    """

    def __init__(self):
        self.duration_converter = StrToDurationConverter()
        self.percentage_converter = StrToPercentageConverter()

    def can_convert(self, source_type: type, target_type: type) -> bool:
        """检查是否可以转换"""
        return source_type is str and target_type is float

    def convert(self, source: Any, target_type: type[float]) -> float:
        """执行转换"""
        if not isinstance(source, str):
            raise ConversionError(f"Expected string, got {type(source).__name__}")

        value_str = source.strip()

        # 1. 尝试时间间隔转换
        if self.duration_converter._is_duration_format(value_str):
            return self.duration_converter.convert(source, target_type)

        # 2. 尝试百分比转换
        if self.percentage_converter._is_percentage_format(value_str):
            return self.percentage_converter.convert(source, target_type)

        # 3. 尝试普通浮点数转换
        try:
            return float(value_str)
        except ValueError as e:
            raise ConversionError(f"Cannot convert '{source}' to float") from e
