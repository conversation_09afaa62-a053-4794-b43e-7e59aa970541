#!/usr/bin/env python
"""
* @author: cz
* @description: Properties 属性源加载器
"""

import re
from pathlib import Path
from typing import Any, Union

from .base import AbstractPropertySourceLoader


class PropertiesPropertySourceLoader(AbstractPropertySourceLoader):
    """Properties 属性源加载器

    支持加载 .properties 格式的配置文件.
    """

    def get_file_extensions(self) -> list[str]:
        """获取支持的文件扩展名"""
        return [".properties"]

    def _parse_content(self, content: str, location: Union[str, Path]) -> dict[str, Any]:
        """解析Properties内容

        Args:
            content: Properties文件内容
            location: 文件位置(用于错误信息)

        Returns:
            解析后的属性字典

        Raises:
            ValueError: Properties解析失败时抛出
        """
        try:
            return self._parse_properties(content)
        except Exception as e:
            raise ValueError(f"Failed to parse Properties content from {location}: {e}") from e

    def _parse_properties(self, content: str) -> dict[str, str]:
        """解析 Properties 文件内容

        Args:
            content: Properties 文件内容

        Returns:
            解析后的属性字典
        """
        properties = {}
        lines = content.splitlines()

        for _line_num, line in enumerate(lines, 1):
            line = line.strip()

            # 跳过空行和注释行
            if not line or line.startswith(("#", "!")):
                continue

            # 查找键值分隔符 (= 或 :)
            match = re.match(r"^([^=:]+)[=:](.*)$", line)
            if match:
                key = match.group(1).strip()
                value = match.group(2).strip()

                # 处理转义字符
                key = self._unescape_string(key)
                value = self._unescape_string(value)

                properties[key] = value
            else:
                # 如果行格式不正确,可以选择忽略或抛出异常
                # 这里选择忽略,但可以记录警告
                pass

        return properties

    def _unescape_string(self, text: str) -> str:
        """处理字符串中的转义字符

        Args:
            text: 原始字符串

        Returns:
            处理转义字符后的字符串
        """
        # 处理常见的转义字符
        replacements = {"\\n": "\n", "\\r": "\r", "\\t": "\t", "\\\\": "\\", "\\=": "=", "\\:": ":", "\\ ": " "}

        result = text
        for escaped, unescaped in replacements.items():
            result = result.replace(escaped, unescaped)

        return result
