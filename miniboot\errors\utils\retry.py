#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Mini-Boot框架重试装饰器

基于异常类的重试属性自动进行重试，简化重试逻辑的使用。
"""

import asyncio
import time
from functools import wraps
from typing import Any, Callable, Optional, type, Union

from loguru import logger

from ..base import MiniBootError
from .strategies import (ExponentialBackoff, FixedBackoff, LinearBackoff,
                         RandomJitterBackoff)


def retry(
    exception_types: Optional[Union[type[Exception], tuple]] = None,
    max_attempts: Optional[int] = None,
    base_delay: Optional[float] = None,
    strategy: Optional[str] = None,
    on_retry: Optional[Callable] = None,
):
    """重试装饰器

    自动从异常类的属性中读取重试配置，也可以通过参数覆盖。

    Args:
        exception_types: 需要重试的异常类型，默认为MiniBootError及其子类
        max_attempts: 最大重试次数，覆盖异常类的配置
        base_delay: 基础延迟时间，覆盖异常类的配置
        strategy: 重试策略，覆盖异常类的配置
        on_retry: 重试时的回调函数
    """
    if exception_types is None:
        exception_types = MiniBootError

    def decorator(func: Callable) -> Callable:
        if asyncio.iscoroutinefunction(func):
            return _async_retry_wrapper(func, exception_types, max_attempts, base_delay, strategy, on_retry)
        else:
            return _sync_retry_wrapper(func, exception_types, max_attempts, base_delay, strategy, on_retry)

    return decorator


def _sync_retry_wrapper(
    func: Callable,
    exception_types: Union[type[Exception], tuple],
    max_attempts: Optional[int],
    base_delay: Optional[float],
    strategy: Optional[str],
    on_retry: Optional[Callable],
) -> Callable:
    """同步函数重试包装器"""

    @wraps(func)
    def wrapper(*args, **kwargs) -> Any:
        last_exception = None

        for attempt in range(1, 100):  # 最大100次，实际由异常类配置控制
            try:
                return func(*args, **kwargs)
            except exception_types as e:
                last_exception = e

                # 获取重试配置
                retry_config = _get_retry_config(e, max_attempts, base_delay, strategy)

                # 检查是否应该重试
                if not retry_config["retryable"] or attempt >= retry_config["max_attempts"]:
                    logger.error(f"Function {func.__name__} failed after {attempt} attempts: {e}")
                    raise e

                # 计算延迟时间
                delay = _calculate_delay(attempt, retry_config)

                # 记录重试日志
                logger.warning(
                    f"Function {func.__name__} failed on attempt {attempt}/{retry_config['max_attempts']}: {e}. "
                    f"Retrying in {delay:.2f}s..."
                )

                # 调用重试回调
                if on_retry:
                    try:
                        on_retry(attempt, e, delay)
                    except Exception as callback_error:
                        logger.error(f"Retry callback failed: {callback_error}")

                # 等待延迟
                time.sleep(delay)
            except Exception as e:
                # 非目标异常类型，直接抛出
                logger.error(f"Function {func.__name__} failed with non-retryable exception: {e}")
                raise e

        # 理论上不会到达这里
        if last_exception:
            raise last_exception

    return wrapper


def _async_retry_wrapper(
    func: Callable,
    exception_types: Union[type[Exception], tuple],
    max_attempts: Optional[int],
    base_delay: Optional[float],
    strategy: Optional[str],
    on_retry: Optional[Callable],
) -> Callable:
    """异步函数重试包装器"""

    @wraps(func)
    async def wrapper(*args, **kwargs) -> Any:
        last_exception = None

        for attempt in range(1, 100):  # 最大100次，实际由异常类配置控制
            try:
                return await func(*args, **kwargs)
            except exception_types as e:
                last_exception = e

                # 获取重试配置
                retry_config = _get_retry_config(e, max_attempts, base_delay, strategy)

                # 检查是否应该重试
                if not retry_config["retryable"] or attempt >= retry_config["max_attempts"]:
                    logger.error(f"Async function {func.__name__} failed after {attempt} attempts: {e}")
                    raise e

                # 计算延迟时间
                delay = _calculate_delay(attempt, retry_config)

                # 记录重试日志
                logger.warning(
                    f"Async function {func.__name__} failed on attempt {attempt}/{retry_config['max_attempts']}: {e}. "
                    f"Retrying in {delay:.2f}s..."
                )

                # 调用重试回调
                if on_retry:
                    try:
                        if asyncio.iscoroutinefunction(on_retry):
                            await on_retry(attempt, e, delay)
                        else:
                            on_retry(attempt, e, delay)
                    except Exception as callback_error:
                        logger.error(f"Retry callback failed: {callback_error}")

                # 等待延迟
                await asyncio.sleep(delay)
            except Exception as e:
                # 非目标异常类型，直接抛出
                logger.error(f"Async function {func.__name__} failed with non-retryable exception: {e}")
                raise e

        # 理论上不会到达这里
        if last_exception:
            raise last_exception

    return wrapper


def _get_retry_config(
    exception: Exception,
    max_attempts: Optional[int],
    base_delay: Optional[float],
    strategy: Optional[str],
) -> dict:
    """从异常实例获取重试配置"""
    config = {
        "retryable": getattr(exception, "retryable", False),
        "max_attempts": max_attempts or getattr(exception, "max_attempts", 1),
        "base_delay": base_delay or getattr(exception, "base_delay", 1.0),
        "strategy": strategy or getattr(exception, "strategy", "exponential"),
    }
    return config


def _calculate_delay(attempt: int, retry_config: dict) -> float:
    """计算延迟时间"""
    strategy_name = retry_config["strategy"]
    base_delay = retry_config["base_delay"]

    # 直接实例化策略类
    if strategy_name == "fixed":
        strategy_instance = FixedBackoff()
    elif strategy_name == "exponential":
        strategy_instance = ExponentialBackoff()
    elif strategy_name == "linear":
        strategy_instance = LinearBackoff()
    elif strategy_name == "random_jitter":
        strategy_instance = RandomJitterBackoff()
    else:
        # 如果策略不存在，使用固定延迟
        logger.warning(f"Unknown retry strategy '{strategy_name}', using fixed delay")
        return base_delay

    return strategy_instance.calculate_delay(attempt, base_delay)
