#!/usr/bin/env python
"""
* @author: cz
* @description: 配置文件自动发现和加载器 - 优化版本
"""

import time
from collections import defaultdict
from dataclasses import dataclass
from pathlib import Path
from typing import Optional

from loguru import logger

from .priority import ConfigurationLayer, get_priority
from .resource import (DefaultResourceLoader, JsonPropertySourceLoader,
                       PropertiesPropertySourceLoader,
                       YamlPropertySourceLoader)
from .sources import CommandLinePropertySource, MutablePropertySources


@dataclass
class ConfigurationLoadResult:
    """配置加载结果"""

    loaded_files: list[Path]
    failed_files: list[tuple[Path, str]]
    load_time_ms: float
    total_sources: int
    layer_statistics: dict[str, int]


@dataclass
class ConfigurationFileInfo:
    """配置文件信息"""

    path: Path
    layer: str
    priority: int
    is_profile_specific: bool
    profile_name: Optional[str] = None


class ConfigurationLoader:
    """优化的配置文件自动发现和加载器

    优化特性:
    1. 框架配置作为基础,用户配置智能覆盖
    2. 配置文件发现缓存,提升性能
    3. 详细的加载统计和错误诊断
    4. 分层加载机制,确保正确的优先级
    5. 智能配置合并策略
    """

    # 默认配置文件名
    DEFAULT_CONFIG_NAMES = ["application", "config"]

    # 支持的文件扩展名
    SUPPORTED_EXTENSIONS = [".yml", ".yaml", ".json", ".properties"]

    def __init__(self, search_locations: Optional[list[str]] = None, enable_cache: bool = True,
                 custom_files: Optional[list[tuple[Path, int]]] = None):
        """初始化优化的配置加载器

        Args:
            search_locations: 搜索路径列表, 如果为 None 则使用默认路径
            enable_cache: 是否启用配置文件发现缓存
            custom_files: 自定义配置文件列表，格式为[(file_path, priority), ...]
        """
        self._priority_manager = get_priority()
        self._resource_loader = DefaultResourceLoader()
        self._enable_cache = enable_cache

        # 配置文件发现缓存
        self._file_discovery_cache: dict[str, list[ConfigurationFileInfo]] = {}
        self._cache_timestamp: float = 0
        self._cache_ttl_seconds: float = 300  # 5分钟缓存

        # 加载统计
        self._load_statistics: dict[str, int] = defaultdict(int)

        # 自定义配置文件
        self._custom_files = custom_files or []

        # 搜索路径设置
        if search_locations:
            self._search_locations = search_locations
            # 创建自定义的搜索路径映射
            self._custom_search_paths = True
            # 假设前半部分是用户路径,后半部分是框架路径
            mid_point = len(search_locations) // 2
            self._user_paths = search_locations[:mid_point] if mid_point > 0 else []
            self._framework_paths = search_locations[mid_point:]
            self._system_paths = []  # 系统路径保持为空,使用默认
        else:
            self._search_locations = self._priority_manager.get_search_path().get_all_paths()
            self._custom_search_paths = False

    def add_custom_config_file(self, file_path: Path, priority: int = 1500) -> None:
        """添加自定义配置文件

        Args:
            file_path: 配置文件路径
            priority: 优先级，数值越高优先级越高
        """
        if not file_path.exists():
            raise FileNotFoundError(f"Custom config file not found: {file_path}")

        if file_path.suffix.lower() not in self.SUPPORTED_EXTENSIONS:
            raise ValueError(f"Unsupported config file extension: {file_path.suffix}")

        self._custom_files.append((file_path, priority))
        logger.debug("添加自定义配置文件: {} (优先级: {})", file_path, priority)

    def load_configuration(self, property_sources: MutablePropertySources, active_profiles: Optional[set[str]] = None) -> ConfigurationLoadResult:
        """优化的配置加载方法 - 框架配置作为基础,用户配置智能覆盖

        加载顺序(从低优先级到高优先级):
        1. 框架默认配置(基础)
        2. 框架Profile特定配置
        3. 用户默认配置(覆盖框架配置)
        4. 用户Profile特定配置
        5. 系统环境变量
        6. 命令行参数(最高优先级)

        Args:
            property_sources: 属性源管理器
            active_profiles: 激活的 Profile 集合

        Returns:
            配置加载结果统计
        """
        start_time = time.time()
        active_profiles = active_profiles or set()

        loaded_files: list[Path] = []
        failed_files: list[tuple[Path, str]] = []
        layer_stats: dict[str, int] = defaultdict(int)

        logger.debug("开始配置加载流程, 激活Profile: {}", list(active_profiles))
        logger.trace("搜索路径: {}", self._search_locations)
        logger.trace("缓存状态: 启用={}, TTL={}s", self._enable_cache, self._cache_ttl_seconds)

        # 第一阶段:建立框架配置基础
        logger.debug("加载框架默认配置...")
        self._load_framework(property_sources, active_profiles, loaded_files, failed_files, layer_stats)

        # 第二阶段:用户配置智能覆盖
        logger.debug("加载用户配置...")
        self._load_user(property_sources, active_profiles, loaded_files, failed_files, layer_stats)

        # 第三阶段:系统级配置(最高优先级)
        logger.debug("加载系统级配置...")
        self._load_system(property_sources, layer_stats)

        # 第四阶段:自定义配置文件(超高优先级)
        if self._custom_files:
            logger.debug("加载自定义配置文件...")
            self._load_custom_files(property_sources, active_profiles, loaded_files, failed_files, layer_stats)

        # 计算加载时间
        load_time = (time.time() - start_time) * 1000

        # 生成加载结果
        result = ConfigurationLoadResult(
            loaded_files=loaded_files,
            failed_files=failed_files,
            load_time_ms=load_time,
            total_sources=len(property_sources._property_sources),
            layer_statistics=dict(layer_stats),
        )

        # 输出加载统计
        self._print_stats(result)

        return result

    def _load_framework(
        self,
        property_sources: MutablePropertySources,
        active_profiles: set[str],
        loaded_files: list[Path],
        failed_files: list[tuple[Path, str]],
        layer_stats: dict[str, int],
    ) -> list[Path]:
        """第一阶段:加载框架配置作为基础

        Args:
            property_sources: 属性源管理器
            active_profiles: 激活的Profile集合
            loaded_files: 已加载文件列表
            failed_files: 加载失败文件列表
            layer_stats: 层级统计

        Returns:
            框架配置文件列表
        """
        framework_files = []

        # 1. 加载框架默认配置(最低优先级基础)
        logger.trace("加载框架默认配置文件...")
        default_files = self._load_layer_configs(property_sources, ConfigurationLayer.FRAMEWORK, None, loaded_files, failed_files, layer_stats)
        framework_files.extend(default_files)

        # 2. 加载框架Profile特定配置
        for profile in active_profiles:
            logger.trace("加载框架Profile配置: {}", profile)
            profile_files = self._load_layer_configs(property_sources, ConfigurationLayer.FRAMEWORK, profile, loaded_files, failed_files, layer_stats)
            framework_files.extend(profile_files)

        logger.debug("框架配置加载完成, 文件数: {}", len(framework_files))
        return framework_files

    def _load_user(
        self,
        property_sources: MutablePropertySources,
        active_profiles: set[str],
        loaded_files: list[Path],
        failed_files: list[tuple[Path, str]],
        layer_stats: dict[str, int],
    ) -> list[Path]:
        """第二阶段:加载用户配置进行智能覆盖

        Args:
            property_sources: 属性源管理器
            active_profiles: 激活的Profile集合
            loaded_files: 已加载文件列表
            failed_files: 加载失败文件列表
            layer_stats: 层级统计

        Returns:
            用户配置文件列表
        """
        user_files = []

        # 1. 加载用户默认配置
        logger.trace("加载用户默认配置文件...")
        default_files = self._load_layer_configs(property_sources, ConfigurationLayer.USER, None, loaded_files, failed_files, layer_stats)
        user_files.extend(default_files)

        # 2. 加载用户Profile特定配置(最高优先级)
        for profile in active_profiles:
            logger.trace("加载用户Profile配置: {}", profile)
            profile_files = self._load_layer_configs(property_sources, ConfigurationLayer.USER, profile, loaded_files, failed_files, layer_stats)
            user_files.extend(profile_files)

        logger.debug("用户配置加载完成, 文件数: {}", len(user_files))
        return user_files

    def _load_system(self, property_sources: MutablePropertySources, layer_stats: dict[str, int]) -> None:
        """第三阶段:加载系统级配置

        Args:
            property_sources: 属性源管理器
            layer_stats: 层级统计
        """
        # 添加命令行参数属性源(最高优先级)
        cmd_source = CommandLinePropertySource()
        cmd_priority = self._priority_manager.get_priority_for_source("commandLine", ConfigurationLayer.SYSTEM)
        cmd_source._priority = cmd_priority
        property_sources.add_first(cmd_source)
        layer_stats[ConfigurationLayer.SYSTEM] += 1
        logger.trace("添加命令行参数配置源, 优先级: {}", cmd_priority)

    def _load_layer_configs(
        self,
        property_sources: MutablePropertySources,
        layer: str,
        profile: Optional[str],
        loaded_files: list[Path],
        failed_files: list[tuple[Path, str]],
        layer_stats: dict[str, int],
    ) -> list[Path]:
        """加载指定层级的配置文件

        Args:
            property_sources: 属性源管理器
            layer: 配置层级
            profile: Profile名称,None表示默认配置
            loaded_files: 已加载文件列表
            failed_files: 加载失败文件列表
            layer_stats: 层级统计

        Returns:
            加载的配置文件列表
        """
        layer_files = []
        search_paths = self._get_search_paths_for_layer(layer)
        is_profile_specific = profile is not None

        # 使用缓存的配置文件发现
        configuration_files = self._discover_cached(search_paths, layer, profile)

        logger.trace("发现配置文件数量: {} (层级: {}, Profile: {})", len(configuration_files), layer, profile or "default")

        for config_info in configuration_files:
            try:
                logger.trace("尝试加载配置文件: {} (优先级: {})", config_info.path, config_info.priority)

                source = self._create_property_source(config_info.path.name, config_info.path, priority=config_info.priority)

                if source:
                    # 根据优先级决定添加位置
                    if is_profile_specific:
                        property_sources.add_first(source)  # Profile配置优先级更高
                        logger.trace("添加到首位 (Profile配置)")
                    else:
                        property_sources.add_last(source)  # 默认配置优先级较低
                        logger.trace("添加到末位 (默认配置)")

                    loaded_files.append(config_info.path)
                    layer_files.append(config_info.path)
                    layer_stats[layer] += 1

                    # 获取属性数量(不同的属性源类型有不同的属性存储方式)
                    prop_count = 0
                    if hasattr(source, "source") and hasattr(source.source, "__len__"):
                        prop_count = len(source.source)
                    elif hasattr(source, "_source") and hasattr(source._source, "__len__"):
                        prop_count = len(source._source)

                    logger.trace("已加载: {} (优先级: {}, 属性数: {})", config_info.path, config_info.priority, prop_count)
                else:
                    logger.warning("配置源创建失败: {}", config_info.path)

            except Exception as e:
                error_msg = f"加载失败: {str(e)}"
                failed_files.append((config_info.path, error_msg))
                logger.error("加载失败: {} - {}", config_info.path, error_msg)

        return layer_files

    def _get_search_paths_for_layer(self, layer: str) -> list[str]:
        """获取指定层的搜索路径

        Args:
            layer: 配置层(user/framework/system)

        Returns:
            搜索路径列表
        """
        # 如果使用自定义搜索路径,直接返回对应层级的路径
        if getattr(self, "_custom_search_paths", False):
            if layer == ConfigurationLayer.USER:
                return self._user_paths
            elif layer == ConfigurationLayer.FRAMEWORK:
                return self._framework_paths
            elif layer == ConfigurationLayer.SYSTEM:
                return self._system_paths
            else:
                return self._search_locations

        # 否则使用默认的优先级管理器搜索路径
        search_path = self._priority_manager.get_search_path()

        if layer == ConfigurationLayer.USER:
            return search_path.get_user_paths()
        elif layer == ConfigurationLayer.FRAMEWORK:
            return search_path.get_framework_paths()
        elif layer == ConfigurationLayer.SYSTEM:
            return search_path.get_system_paths()
        else:
            return search_path.get_all_paths()

    def _discover_cached(self, search_paths: list[str], layer: str, profile: Optional[str]) -> list[ConfigurationFileInfo]:
        """缓存的配置文件发现

        Args:
            search_paths: 搜索路径列表
            layer: 配置层级
            profile: Profile名称

        Returns:
            配置文件信息列表
        """
        if not self._enable_cache:
            return self._discover_configuration_files(search_paths, layer, profile)

        # 生成缓存键
        cache_key = f"{layer}:{profile or 'default'}:{':'.join(search_paths)}"
        current_time = time.time()

        # 检查缓存是否有效
        if cache_key in self._file_discovery_cache and current_time - self._cache_timestamp < self._cache_ttl_seconds:
            logger.trace("使用缓存的配置文件发现结果: {}", cache_key)
            return self._file_discovery_cache[cache_key]

        # 重新发现配置文件
        logger.trace("重新发现配置文件: {}", cache_key)
        configuration_files = self._discover_configuration_files(search_paths, layer, profile)

        # 更新缓存
        self._file_discovery_cache[cache_key] = configuration_files
        self._cache_timestamp = current_time
        logger.trace("缓存已更新, 发现文件数: {}", len(configuration_files))

        return configuration_files

    def _discover_configuration_files(self, search_paths: list[str], layer: str, profile: Optional[str]) -> list[ConfigurationFileInfo]:
        """发现配置文件

        Args:
            search_paths: 搜索路径列表
            layer: 配置层级
            profile: Profile名称

        Returns:
            配置文件信息列表
        """
        configuration_files = []
        is_profile_specific = profile is not None

        logger.trace("开始发现配置文件 - 层级: {}, Profile: {}, 搜索路径数: {}", layer, profile or "default", len(search_paths))

        for config_name in self.DEFAULT_CONFIG_NAMES:
            for location in search_paths:
                for extension in self.SUPPORTED_EXTENSIONS:
                    # 构建文件名
                    file_name = f"{config_name}-{profile}{extension}" if is_profile_specific else f"{config_name}{extension}"

                    # 解析文件路径
                    file_path = self._resolve_path(location, file_name)
                    if file_path and file_path.exists():
                        # 获取优先级
                        priority = self._priority_manager.get_priority_for_source(file_name, layer, is_profile_specific)

                        logger.trace("发现配置文件: {} (优先级: {})", file_path, priority)

                        # 创建配置文件信息
                        config_info = ConfigurationFileInfo(
                            path=file_path, layer=layer, priority=priority, is_profile_specific=is_profile_specific, profile_name=profile
                        )
                        configuration_files.append(config_info)
                    # 移除文件不存在的trace日志以减少噪音

        # 按优先级排序(高优先级在前)
        configuration_files.sort(key=lambda x: x.priority, reverse=True)
        logger.trace("配置文件发现完成, 总数: {} (已按优先级排序)", len(configuration_files))
        return configuration_files

    def _resolve_path(self, location: str, file_name: str) -> Optional[Path]:
        """解析文件路径

        Args:
            location: 搜索位置
            file_name: 文件名

        Returns:
            解析后的路径, 如果无效返回 None
        """
        if location.startswith("classpath:"):
            # 处理 classpath 路径
            classpath_location = location[10:]
            if classpath_location.startswith("/"):
                classpath_location = classpath_location[1:]
            return Path(classpath_location) / file_name
        else:
            # 处理普通文件路径
            return Path(location) / file_name

    def _create_property_source(self, name: str, file_path: Path, priority: int):
        """创建属性源

        Args:
            name: 属性源名称
            file_path: 文件路径
            priority: 优先级

        Returns:
            属性源实例, 如果创建失败返回 None
        """
        try:
            extension = file_path.suffix.lower()
            resource_loader = DefaultResourceLoader()

            if extension in [".yml", ".yaml"]:
                loader = YamlPropertySourceLoader()
                property_sources = loader.load(name, resource_loader, file_path)
                if property_sources:
                    source = property_sources[0]
                    source._priority = priority
                    return source
            elif extension == ".json":
                loader = JsonPropertySourceLoader()
                property_sources = loader.load(name, resource_loader, file_path)
                if property_sources:
                    source = property_sources[0]
                    source._priority = priority
                    return source
            elif extension == ".properties":
                loader = PropertiesPropertySourceLoader()
                property_sources = loader.load(name, resource_loader, file_path)
                if property_sources:
                    source = property_sources[0]
                    source._priority = priority
                    return source

            return None

        except Exception as e:
            # 记录错误但不中断加载过程
            logger.warning("配置文件加载失败: {} - {}", file_path, str(e))
            return None

    def _print_stats(self, result: ConfigurationLoadResult) -> None:
        """打印加载统计信息

        Args:
            result: 配置加载结果
        """
        logger.info("配置加载完成: 成功 {} 个文件, 失败 {} 个文件, 耗时 {:.2f}ms",
                   len(result.loaded_files), len(result.failed_files), result.load_time_ms)

        logger.debug("分层统计:")
        for layer, count in result.layer_statistics.items():
            logger.debug("  {}: {} 个配置源", layer, count)

        logger.trace("已加载的配置文件:")
        for file_path in result.loaded_files:
            logger.trace("  {}", file_path)

        if result.failed_files:
            logger.warning("配置加载失败详情:")
            for file_path, error in result.failed_files:
                logger.warning("  {}: {}", file_path, error)

    def clear_cache(self) -> None:
        """清除配置文件发现缓存"""
        cache_size = len(self._file_discovery_cache)
        self._file_discovery_cache.clear()
        self._cache_timestamp = 0
        logger.debug("配置文件发现缓存已清除, 清除条目数: {}", cache_size)

    def get_load_statistics(self) -> dict[str, int]:
        """获取加载统计信息"""
        return dict(self._load_statistics)

    def set_cache_ttl(self, ttl_seconds: float) -> None:
        """设置缓存TTL

        Args:
            ttl_seconds: 缓存生存时间(秒)
        """
        self._cache_ttl_seconds = ttl_seconds

    def discover_config_files(self, active_profiles: Optional[set[str]] = None) -> list[Path]:
        """发现所有可用的配置文件

        Args:
            active_profiles: 激活的 Profile 集合

        Returns:
            发现的配置文件路径列表
        """
        active_profiles = active_profiles or set()
        config_files = []

        # 发现默认配置文件
        for config_name in self.DEFAULT_CONFIG_NAMES:
            for location in self._search_locations:
                for extension in self.SUPPORTED_EXTENSIONS:
                    file_path = self._resolve_path(location, f"{config_name}{extension}")
                    if file_path and file_path.exists():
                        config_files.append(file_path)

        # 发现 Profile 特定配置文件
        for profile in active_profiles:
            for config_name in self.DEFAULT_CONFIG_NAMES:
                for location in self._search_locations:
                    for extension in self.SUPPORTED_EXTENSIONS:
                        file_name = f"{config_name}-{profile}{extension}"
                        file_path = self._resolve_path(location, file_name)
                        if file_path and file_path.exists():
                            config_files.append(file_path)

        return config_files

    def load_specific_files(self, property_sources: MutablePropertySources, config_files: list[Path], layer: str = ConfigurationLayer.USER) -> None:
        """加载指定的配置文件列表

        Args:
            property_sources: 属性源管理器
            config_files: 要加载的配置文件路径列表
            layer: 配置层(user/framework/system)
        """
        for config_file in config_files:
            if config_file.exists():
                # 根据层级确定优先级
                priority = self._priority_manager.get_priority_for_source(str(config_file), layer, is_profile_specific=False)
                source = self._create_property_source(str(config_file), config_file, priority=priority)
                if source:
                    property_sources.add_first(source)
                    # 获取属性数量
                    prop_count = 0
                    if hasattr(source, "source") and hasattr(source.source, "__len__"):
                        prop_count = len(source.source)
                    elif hasattr(source, "_source") and hasattr(source._source, "__len__"):
                        prop_count = len(source._source)

                    logger.debug("已加载指定配置文件: {} (层级: {}, 优先级: {}, 属性数: {})", config_file, layer, priority, prop_count)

    def _load_custom_files(self, property_sources: MutablePropertySources, _active_profiles: set[str],
                          loaded_files: list[Path], failed_files: list[tuple[Path, str]],
                          layer_stats: dict[str, int]) -> None:
        """加载自定义配置文件

        Args:
            property_sources: 属性源管理器
            active_profiles: 激活的Profile集合
            loaded_files: 已加载文件列表
            failed_files: 加载失败文件列表
            layer_stats: 层级统计
        """
        for file_path, priority in self._custom_files:
            try:
                if not file_path.exists():
                    failed_files.append((file_path, f"File not found: {file_path}"))
                    continue

                # 创建属性源
                source = self._create_property_source(str(file_path), file_path, priority=priority)
                if source:
                    property_sources.add_first(source)
                    loaded_files.append(file_path)
                    layer_stats["custom"] += 1

                    # 获取属性数量
                    prop_count = 0
                    if hasattr(source, "source") and hasattr(source.source, "__len__"):
                        prop_count = len(source.source)
                    elif hasattr(source, "_source") and hasattr(source._source, "__len__"):
                        prop_count = len(source._source)

                    logger.debug("已加载自定义配置文件: {} (优先级: {}, 属性数: {})",
                               file_path, priority, prop_count)
                else:
                    failed_files.append((file_path, "Failed to create property source"))

            except Exception as e:
                error_msg = f"Failed to load custom config file {file_path}: {e}"
                logger.warning(error_msg)
                failed_files.append((file_path, error_msg))
