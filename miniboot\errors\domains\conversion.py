#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Conversion相关异常类

类型转换系统相关的异常。
"""

from ..base import BusinessError, ValidationError


# 转换执行相关异常 (BusinessError)
class ConversionError(BusinessError):
    """转换错误 - 类型转换过程中的错误"""
    max_attempts = 2
    base_delay = 0.5

    def __init__(self, message: str, source_type: type = None, target_type: type = None, **kwargs):
        """初始化转换错误

        Args:
            message: 错误消息
            source_type: 源类型
            target_type: 目标类型
            **kwargs: 其他参数
        """
        # 构建详细信息
        details = kwargs.get('details', {})
        if source_type:
            details['source_type'] = source_type.__name__
        if target_type:
            details['target_type'] = target_type.__name__

        kwargs['details'] = details
        super().__init__(message, **kwargs)

        # 保持向后兼容的属性
        self.source_type = source_type
        self.target_type = target_type


class ConversionRegistryError(BusinessError):
    """转换注册器错误 - 转换注册器相关的错误"""
    max_attempts = 2
    base_delay = 0.5


class ConverterRegistrationError(BusinessError):
    """转换器注册错误 - 转换器注册过程中的错误"""
    max_attempts = 2
    base_delay = 0.5


# 转换验证相关异常 (ValidationError)
class InvalidConverterError(ValidationError):
    """无效转换器错误 - 转换器无效"""
    # 继承ValidationError的属性：retryable = False


class UnsupportedConversionError(ValidationError):
    """不支持的转换错误 - 不支持的类型转换"""
    # 继承ValidationError的属性：retryable = False


class ConversionChainError(BusinessError):
    """转换链错误 - 链式转换过程中的错误"""
    max_attempts = 2
    base_delay = 0.5


class ConversionPathNotFoundError(ValidationError):
    """转换路径未找到错误 - 找不到转换路径"""
    # 继承ValidationError的属性：retryable = False
