#!/usr/bin/env python
"""
* @author: cz
* @description: 横幅资源加载器
"""

import multiprocessing
import os
import sys
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Optional

from miniboot.env.resource.loader import DefaultResourceLoader
from miniboot.errors import ResourceNotFoundError
from .constants import BannerConstants


class BannerResource(ABC):
    """横幅资源抽象基类"""

    @abstractmethod
    def content(self) -> str:
        """获取横幅内容"""
        pass

    @abstractmethod
    def variables(self) -> dict[str, Any]:
        """获取模板变量"""
        pass

    def set_variable(self, key: str, value: Any) -> None:
        """设置模板变量（默认实现，子类可以重写）"""
        pass


class DefaultBannerResource(BannerResource):
    """默认横幅资源"""

    def __init__(self, application_name: str = BannerConstants.DEFAULT_APPLICATION_NAME, application_version: str = BannerConstants.DEFAULT_APPLICATION_VERSION):
        self.application_name = application_name
        self.application_version = application_version
        self._custom_variables = {}

    def content(self) -> str:
        """获取默认横幅内容"""
        return """
 _____ _____ _____ _____     _____ _____ _____ _____
|     |     |   | |     |___|  _  |     |     |_   _|
| | | |-   -| | | |-   -|___|   __|  |  |  |  | | |
|_|_|_|_____|_|___|_____|   |__|  |_____|_____|_|_|

 :: ${application.name} :: (v${application.version})
 :: Powered by Mini-Boot :: (v${miniboot.version})
"""

    def variables(self) -> dict[str, Any]:
        """获取模板变量"""
        # 获取操作系统信息
        try:
            import platform

            if os.name == "nt":
                os_name = "Windows"
                # 获取Windows版本信息
                try:
                    os_version = platform.release()  # 如 '10', '11'
                    if not os_version or os_version == "unknown":
                        os_version = platform.version().split(".")[0] if platform.version() else "unknown"
                except Exception:
                    os_version = "unknown"

                # 获取架构信息
                try:
                    os_arch = platform.machine().lower()
                    if os_arch in ["amd64", "x86_64", "x64"]:
                        os_arch = "amd64"
                    elif os_arch in ["i386", "i686"]:
                        os_arch = "386"
                    os_platform = f"{os_name}/{os_arch}"
                except Exception:
                    os_platform = f"{os_name}/unknown"
            else:
                # Unix/Linux系统
                os_name = platform.system()
                os_version = platform.release()
                try:
                    os_arch = platform.machine().lower()
                    os_platform = f"{os_name}/{os_arch}"
                except Exception:
                    os_platform = f"{os_name}/unknown"
        except Exception:
            os_name = "unknown"
            os_version = "unknown"
            os_platform = "unknown/unknown"

        # 获取CPU核心数
        try:
            cpu_cores = multiprocessing.cpu_count()
        except Exception:
            cpu_cores = "unknown"

        variables = {
            "miniboot.version": "0.0.4",
            "miniboot.formatted-version": "0.0.4",
            "application.name": self.application_name,
            "application.version": self.application_version,
            "application.formatted-version": f"({self.application_version})",
            "python.version": sys.version.split()[0],
            "python.vendor": sys.version.split()[0],
            "os.name": os_name,
            "os.platform": os_platform,
            "os.version": os_version,
            "cpu.cores": cpu_cores,
            "startup.time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "startup.date": datetime.now().strftime("%Y-%m-%d"),
        }

        # 合并自定义变量
        variables.update(self._custom_variables)
        return variables

    def set_variable(self, key: str, value: Any) -> None:
        """设置自定义模板变量"""
        self._custom_variables[key] = value


class FileBannerResource(BannerResource):
    """文件横幅资源"""

    def __init__(self, file_path: str, charset: str = "UTF-8", application_name: str = "Mini-Boot Application", application_version: str = "0.0.4"):
        self.file_path = file_path
        self.charset = charset
        self.application_name = application_name
        self.application_version = application_version
        self._content: Optional[str] = None

    def content(self) -> str:
        """获取文件横幅内容"""
        if self._content is None:
            self._load()
        return self._content or ""

    def _load(self):
        """加载文件内容"""
        try:
            loader = DefaultResourceLoader()
            self._content = loader.load(self.file_path)
        except (ResourceNotFoundError, FileNotFoundError, OSError):
            # 如果文件不存在或读取失败,使用默认横幅
            default_resource = DefaultBannerResource(self.application_name, self.application_version)
            self._content = default_resource.content()

    def variables(self) -> dict[str, Any]:
        """获取模板变量"""
        default_resource = DefaultBannerResource(self.application_name, self.application_version)
        return default_resource.variables()


class BannerResourceLoader:
    """横幅资源加载器"""

    def __init__(self):
        self.resource_loader = DefaultResourceLoader()

    def load(
        self,
        location: Optional[str] = None,
        charset: str = BannerConstants.DEFAULT_CHARSET,
        application_name: str = BannerConstants.DEFAULT_APPLICATION_NAME,
        application_version: str = BannerConstants.DEFAULT_APPLICATION_VERSION,
    ) -> BannerResource:
        """加载横幅资源

        Args:
            location: 横幅文件位置,支持 classpath: 和文件路径
            charset: 文件字符编码
            application_name: 应用名称
            application_version: 应用版本

        Returns:
            BannerResource: 横幅资源对象
        """
        if not location:
            return DefaultBannerResource(application_name, application_version)

        # 处理 classpath: 前缀
        if location.startswith("classpath:"):
            # 移除 classpath: 前缀,在项目根目录查找
            file_path = location[10:]  # 移除 "classpath:"
            if not file_path.startswith("/"):
                # 相对路径,在 resources 目录查找
                file_path = f"resources/{file_path}"
        else:
            file_path = location

        return FileBannerResource(file_path, charset, application_name, application_version)

    def locations(self) -> list[str]:
        """获取默认横幅文件位置"""
        return ["classpath:banner.txt", "classpath:banner.ascii", "banner.txt", "banner.ascii"]

    def find(self, application_name: str = BannerConstants.DEFAULT_APPLICATION_NAME, application_version: str = BannerConstants.DEFAULT_APPLICATION_VERSION) -> BannerResource:
        """查找横幅资源

        按默认位置顺序查找横幅文件,找到第一个存在的文件就返回

        Args:
            application_name: 应用名称
            application_version: 应用版本

        Returns:
            BannerResource: 横幅资源对象
        """
        for location in self.locations():
            try:
                resource = self.load(location, BannerConstants.DEFAULT_CHARSET, application_name, application_version)
                # 尝试获取内容,如果成功说明文件存在
                content = resource.content()
                if content and content.strip():
                    return resource
            except Exception:
                continue

        # 如果都没找到,返回默认横幅
        return DefaultBannerResource(application_name, application_version)
