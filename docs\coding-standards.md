# Mini-Boot Python 编码规范

## 概述

为确保 Mini-Boot 项目代码质量和一致性，制定以下编码规范。所有代码必须严格遵循这些规范，确保无编译告警。

## 项目运行环境规范

### Python 执行严格要求

**🚨 强制规定：所有 Python 文件的运行必须通过 `uv run python` 执行**

```bash
# ✅ 正确：使用 uv run python
uv run python script.py
uv run python -m module_name
uv run python tests/test_runner.py

# ❌ 错误：禁止直接使用 python
python script.py           # 禁止
python -m module_name      # 禁止
python3 script.py          # 禁止
py script.py               # 禁止
```

**适用范围：**

- 所有脚本执行
- 测试运行
- 代码质量检查
- 开发工具调用
- CI/CD 流水线
- 文档示例命令

**违规处理：**

- 代码审查时发现直接使用 `python` 命令的，必须修改为 `uv run python`
- 文档中的示例命令必须使用 `uv run python`
- Git 钩子和自动化脚本必须使用 `uv run python`

## 代码格式规范

### 1. 文件头格式

所有 Python 文件必须使用统一的头文件格式：

```python
#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 模块功能描述
"""
```

**格式说明**：

- **第一行**：`#!/usr/bin/env python` - Python 解释器声明
- **第二行**：`# encoding: utf-8` - 文件编码声明
- **第三行**：空行
- **文档字符串**：使用三重双引号，包含作者和描述信息
  - `@author`: 作者信息，统一使用 "cz"
  - `@description`: 模块功能的简要描述

**示例**：

```python
#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 环境配置模块 - 提供环境变量管理和配置文件加载功能
"""

import os
from typing import Optional

class Environment:
    """环境管理类"""
    pass
```

### 2. 缩进和空白

- **缩进**：使用 4 个空格，禁用制表符
- **行尾空白**：严禁任何尾随空白字符
- **行长度**：最大 150 字符（符合项目 ruff 配置）
- **空行**：
  - 类定义前后 2 个空行
  - 函数定义前后 1 个空行
  - 方法定义前后 1 个空行

### 3. 导入规范

```python
# 标准库导入
import os
import sys
from pathlib import Path
from typing import Dict, List, Optional

# 第三方库导入
import yaml
import requests

# 本地导入
from miniboot.core import Container
from miniboot.annotations import Component
```

### 4. 命名规范

#### 4.1 基本命名规则

- **类名**：PascalCase (`BannerManager`)
- **函数/方法名**：snake_case (`get_current_version`)
- **变量名**：snake_case (`config_file`)
- **常量名**：UPPER_SNAKE_CASE (`DEFAULT_PORT`)
- **私有成员**：前缀下划线 (`_private_method`)

#### 4.2 函数命名简化规范

**核心原则**：追求简洁性和可读性，去除冗余前缀，保留核心语义

##### 4.2.1 简化策略

1. **去除冗余前缀**

   ```python
   # ❌ 冗余命名
   def _get_beans_info(self) -> dict:
   def _collect_beans(self) -> dict:
   def _create_bean_info(self) -> dict:

   # ✅ 简化命名
   def get(self) -> dict:
   def _collect(self) -> dict:
   def _build(self) -> dict:
   ```

2. **保留核心动词**

   ```python
   # ❌ 过长命名
   def _get_bean_dependencies(self) -> list:
   def _collect_statistics(self) -> dict:
   def set_application_context(self) -> None:

   # ✅ 核心动词
   def _deps(self) -> list:
   def _stats(self) -> dict:
   def set_context(self) -> None:
   ```

3. **统一命名风格**

   ```python
   # ❌ 不一致命名
   def _get_bean_aliases(self) -> list:
   def _get_bean_resource(self) -> str:
   def _get_instance_info(self) -> dict:

   # ✅ 一致简化
   def _aliases(self) -> list:
   def _resource(self) -> str:
   def _instance(self) -> dict:
   ```

##### 4.2.2 命名长度指导

- **公共方法**：1-2 个单词 (`get`, `create`, `update`)
- **私有方法**：1-3 个单词 (`_build`, `_collect`, `_validate`)
- **工具方法**：简化缩写可接受 (`_deps`, `_stats`, `_props`)

##### 4.2.3 上下文相关命名

在类的上下文中，方法名可以更简洁：

```python
class BeansEndpoint:
    def get(self):          # 在 BeansEndpoint 中，get 明确指获取 beans
        pass

    def _collect(self):     # 在此上下文中，collect 明确指收集 bean 信息
        pass

class CacheManager:
    def get(self, name):    # 在 CacheManager 中，get 明确指获取缓存
        pass

    def remove(self, name): # remove 明确指移除缓存
        pass
```

##### 4.2.4 简化效果目标

- **平均简化率**：50-70%
- **最大长度**：私有方法不超过 15 字符
- **可读性**：保持语义清晰的前提下最大化简洁性

### 5. 字符串规范

- **优先使用双引号**：`"hello world"`
- **文档字符串使用三重双引号**：`"""Documentation string"""`
- **格式化字符串使用 f-string**：`f"Version: {version}"`

### 6. 语言规范

- **注释和文档字符串可以使用中文**：为提高代码可读性，允许使用中文注释
- **中文注释标点符号规范**：
  - **必须使用英文标点符号**：逗号(,)、句号(.)、分号(;)、冒号(:)、感叹号(!)、问号(?)等
  - **禁止使用中文标点符号**：避免使用中文逗号(，)、中文句号(。)、中文冒号(：)、中文分号(；)等
  - **括号统一使用英文括号**：()、[]、{}，禁止使用（）、【】、《》
  - **引号统一使用英文引号**：""、''，禁止使用""、''
- **变量命名使用英文**：避免使用中文拼音或中文字符
- **异常消息可以使用中文**：用户面向的错误消息可以使用中文以提高可读性，但标点符号仍需使用英文

**正确示例**：

```python
class Environment(PropertyResolver, ABC):
    """环境接口

    扩展 PropertyResolver, 添加 Profile 管理功能.
    """

    def get_active_profiles(self) -> list[str]:
        """获取激活的 Profile 列表

        Returns:
            激活的 Profile 名称列表
        """
        # 从配置中获取激活的 profiles, 如果没有则返回默认值
        profiles = self.get_property('miniboot.profiles.active', 'default')
        return profiles.split(',') if isinstance(profiles, str) else []
```

**错误示例**：

```python
class Environment(PropertyResolver, ABC):
    """环境接口

    扩展 PropertyResolver，添加 Profile 管理功能。  # ❌ 中文逗号和句号
    """

    def get_active_profiles(self) -> list[str]:
        """获取激活的 Profile 列表。  # ❌ 中文句号

        Returns：  # ❌ 中文冒号
            激活的 Profile 名称列表
        """
        # 从配置中获取激活的 profiles，如果没有则返回默认值  # ❌ 中文逗号
        profiles = self.get_property（'miniboot.profiles.active'，'default'）  # ❌ 中文括号和逗号
        return profiles.split（'，'） if isinstance（profiles，str） else []  # ❌ 多处中文标点
```

## 代码质量规范

### 1. 异常处理

```python
# ✅ 正确：具体异常类型
try:
    result = some_operation()
except (ValueError, TypeError) as e:
    logger.error(f"操作失败: {e}")

# ❌ 错误：过于宽泛
try:
    result = some_operation()
except Exception as e:  # 避免使用
    logger.error(f"操作失败: {e}")
```

### 2. 函数参数

```python
# ✅ 正确：所有参数都被使用
def process_data(data: dict, config: dict) -> bool:
    result = validate_data(data)
    settings = load_settings(config)
    return result and settings

# ❌ 错误：未使用的参数
def process_data(data: dict, unused_param: dict) -> bool:
    return validate_data(data)
```

### 3. 导入管理

```python
# ✅ 正确：只导入使用的模块
from pathlib import Path
from typing import Optional

def get_file_size(file_path: Optional[Path]) -> int:
    if file_path and file_path.exists():
        return file_path.stat().st_size
    return 0

# ❌ 错误：导入但未使用
import os  # 如果未使用则删除
from typing import Dict, List  # 如果未使用则删除
```

### 4. 类型注解

```python
# ✅ 正确：完整的类型注解
def update_version(self, version_type: str) -> str:
    """更新版本号"""
    pass

def load_config(self, config_file: Optional[str] = None) -> Dict[str, Any]:
    """加载配置文件"""
    pass
```

## 设计模式规范

### 1. 单例模式

对于需要全局唯一实例的类（如配置管理器、缓存管理器等），必须使用标准的单例模式实现。

#### 实现规范

**✅ 正确：使用 SingletonMeta 元类**

```python
from miniboot.utils.singleton import SingletonMeta

class CacheManager(metaclass=SingletonMeta):
    """缓存管理器 - 全局单例"""

    def __init__(self):
        """初始化缓存管理器"""
        if not hasattr(self, '_initialized'):
            self._cache = {}
            self._initialized = True

    def get_cache(self, key: str) -> any:
        """获取缓存"""
        # 实现逻辑
        pass
```

**❌ 错误：使用全局变量和便捷函数**

```python
# 不要这样做
class CacheManager:
    def __init__(self):
        self._cache = {}

# 全局实例
_cache_manager = CacheManager()

def get_cache_manager() -> CacheManager:
    """获取全局缓存管理器实例"""
    return _cache_manager

def get_cache(key: str) -> any:
    """便捷函数"""
    return _cache_manager.get_cache(key)
```

#### 使用规范

**调用方负责初始化**

```python
# ✅ 正确：调用方控制初始化时机
from miniboot.cache import CacheManager

class SomeService:
    def __init__(self):
        # 第一次调用时自动初始化，后续调用返回同一实例
        self.cache_manager = CacheManager()

    def get_data(self, key: str) -> any:
        return self.cache_manager.get_cache(key)

# 验证单例行为
manager1 = CacheManager()
manager2 = CacheManager()
assert manager1 is manager2  # True
```

**❌ 错误：使用便捷函数**

```python
# 不要这样做
from miniboot.cache import get_cache_manager, get_cache

# 隐式依赖全局状态
manager = get_cache_manager()
result = get_cache("key")
```

#### 测试支持

**单例重置（仅测试时使用）**

```python
import unittest
from miniboot.utils.singleton import SingletonMeta
from miniboot.cache import CacheManager

class TestCacheManager(unittest.TestCase):
    def setUp(self):
        # 测试前重置单例状态
        SingletonMeta.reset_instance(CacheManager)

    def test_singleton_behavior(self):
        manager1 = CacheManager()
        manager2 = CacheManager()
        self.assertIs(manager1, manager2)
```

#### 适用场景

**应该使用单例模式的类**：

- 配置管理器（ConfigurationPriorityManager）
- 缓存管理器（CacheManager）
- 日志管理器（LogManager）
- 性能指标收集器（MetricsCollector）
- 事件总线（EventBus）

**不应该使用单例模式的类**：

- 数据传输对象（DTO）
- 业务实体类（Entity）
- 服务类（Service）- 除非确实需要全局唯一
- 工具类（Utility）- 通常使用静态方法

#### 优势

1. **线程安全**：SingletonMeta 使用双重检查锁定模式
2. **延迟初始化**：首次使用时才创建实例
3. **测试友好**：支持单例重置，便于单元测试
4. **内存效率**：全局唯一实例，避免重复创建
5. **职责清晰**：调用方控制初始化时机

## 文档规范

### 1. 文档字符串

```python
def publish_to_repository(self, url: str, username: str, password: str) -> bool:
    """
    发布包到私有仓库

    Args:
        url: 仓库地址
        username: 用户名
        password: 密码

    Returns:
        发布是否成功

    Raises:
        ConnectionError: 网络连接失败
        AuthenticationError: 认证失败
    """
    pass
```

### 2. 注释规范

#### 基本格式

```python
# 单行注释使用 # 开头，后跟一个空格
result = calculate_value()  # 行尾注释

# 多行注释每行都使用 #
# 这是一个复杂的算法
# 需要多行解释
complex_algorithm()
```

#### 中文注释标点符号规范

**标点符号对照表**：

| 中文标点 | 英文标点 | 说明   |
| -------- | -------- | ------ |
| ，       | ,        | 逗号   |
| 。       | .        | 句号   |
| ：       | :        | 冒号   |
| ；       | ;        | 分号   |
| ！       | !        | 感叹号 |
| ？       | ?        | 问号   |
| （）     | ()       | 圆括号 |
| 【】     | []       | 方括号 |
| 《》     | <>       | 尖括号 |
| ""       | ""       | 双引号 |
| ''       | ''       | 单引号 |

**✅ 正确示例**：

```python
class Environment:
    """环境接口

    扩展 PropertyResolver, 添加 Profile 管理功能.
    """

    def get_profiles(self) -> list[str]:
        """获取 Profile 列表

        Args:
            profiles: Profile 名称列表

        Returns:
            激活的 Profile 名称列表
        """
        # 从配置文件中读取 profiles, 支持逗号分隔的多个值
        return self._load_profiles_from_config()
```

**❌ 错误示例**：

```python
class Environment:
    """环境接口

    扩展 PropertyResolver，添加 Profile 管理功能。  # ❌ 中文逗号和句号
    """

    def get_profiles(self) -> list[str]:
        """获取 Profile 列表。  # ❌ 中文句号

        Args：  # ❌ 中文冒号
            profiles： Profile 名称列表  # ❌ 中文冒号
        """
        # 从配置文件中读取 profiles，支持逗号分隔的多个值  # ❌ 中文逗号
        return self._load_profiles_from_config（）  # ❌ 中文括号
```

## AI 代码生成规范

### 概述

为确保 AI 助手生成的代码严格遵循 Mini-Boot 项目的代码质量标准，制定以下 AI 代码生成专用规范。所有 AI 生成的代码必须符合项目的 ruff 配置要求。

### 1. AI 代码生成文件头部规范

AI 生成的代码必须使用项目标准的文件头部格式：

```python
#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 模块功能描述 - 详细说明模块的主要功能和用途
"""
```

**注意事项**：

- 保持与项目现有代码一致的格式
- `@description` 应该详细描述模块功能
- 必须包含编码声明和 shebang

### 2. AI 代码生成导入规范

```python
# 标准库导入 (按字母顺序)
import os
import sys
from pathlib import Path
from typing import Dict, List, Optional, Union

# 第三方库导入 (按字母顺序)
from fastapi import HTTPException
from loguru import logger

# 本地导入 (按层级顺序)
from ..errors import MiniBootError
from .response import ApiResponse
```

### 3. AI 代码生成类定义规范

```python
class ExampleService:
    """示例服务类

    详细描述类的功能和用途.

    Attributes:
        config: 配置字典
        logger: 日志记录器
    """

    def __init__(self, config: Optional[Dict[str, str]] = None) -> None:
        """初始化服务

        Args:
            config: 可选配置字典
        """
        self.config = config or {}
        logger.info("ExampleService initialized")
```

### 4. AI 代码生成函数规范

```python
async def process_data(self, data: list[str]) -> dict[str, int]:
    """处理数据

    Args:
        data: 输入数据列表

    Returns:
        处理结果字典

    Raises:
        ValueError: 当数据为空时
        MiniBootError: 当处理失败时
    """
    if not data:
        raise ValueError("数据不能为空")

    try:
        result = {}
        for item in data:
            result[item] = len(item)

        logger.debug(f"处理了 {len(data)} 条数据")
        return result

    except Exception as e:
        logger.error(f"数据处理失败: {e}")
        raise MiniBootError(f"数据处理失败: {e}") from e
```

### 5. AI 代码生成检查清单

#### 生成代码前检查

- [ ] 确认模块用途和功能
- [ ] 规划类和函数结构
- [ ] 确定导入依赖
- [ ] 设计异常处理策略
- [ ] 确保使用项目标准文件头部格式

#### 生成代码后检查

- [ ] 文件头部格式正确（包含@author: cz）
- [ ] 导入顺序符合规范
- [ ] 所有函数有完整类型注解
- [ ] 所有公共函数有详细文档字符串
- [ ] 异常处理具体明确
- [ ] 变量命名符合 snake_case 规范
- [ ] 行长度不超过 150 字符
- [ ] 中文注释使用英文标点符号

### 6. AI 代码生成常见问题修复

#### 导入问题

```python
# ❌ 错误
from typing import *
import sys, os

# ✅ 正确
import os
import sys
from typing import Dict, List, Optional
```

#### 类型注解问题

```python
# ❌ 错误
def process_data(data):
    return data

# ✅ 正确
def process_data(data: List[str]) -> List[str]:
    return data
```

#### 异常处理问题

```python
# ❌ 错误
try:
    risky_operation()
except:
    pass

# ✅ 正确
try:
    risky_operation()
except SpecificException as e:
    logger.error(f"操作失败: {e}")
    raise
```

### 7. AI 代码生成防错机制

#### 🚨 强制性代码生成前检查

**在生成任何代码之前，AI 必须执行以下检查步骤：**

1. **现有代码分析**：

   ```bash
   # 分析相关的现有代码结构
   uv run python scripts/code_analyzer.py --analyze-class ClassName
   uv run python scripts/code_analyzer.py --analyze-module module_name
   ```

2. **接口完整性检查**：

   ```bash
   # 检查类的所有方法是否完整实现
   uv run python scripts/interface_checker.py --check-class ClassName
   ```

3. **依赖关系验证**：
   ```bash
   # 验证所有被调用的方法是否存在
   uv run python scripts/dependency_checker.py --verify-calls file.py
   ```

#### 代码生成质量门禁

**🛑 禁止生成代码的情况：**

- 未分析现有相关代码结构
- 未验证被调用方法的存在性
- 未检查接口完整性
- 未确认类型注解的正确性

**✅ 允许生成代码的前提条件：**

- 已完成现有代码分析
- 已验证所有依赖关系
- 已确认接口完整性
- 已通过类型检查

#### 自动化验证流程

```bash
# 完整的AI代码生成验证流程
uv run python scripts/ai_code_validator.py --pre-check module_name
# 生成代码...
uv run python scripts/ai_code_validator.py --post-check generated_file.py
```

#### 验证工具使用

1. **生成前验证**：分析现有代码，确保理解完整
2. **生成中监控**：实时检查代码完整性
3. **生成后验证**：全面检查代码质量和正确性

## 工具配置

### 1. Ruff 配置 (ruff.toml)

```toml
# Ruff 配置文件 - Mini-Boot 项目代码质量标准
# 行长度限制
line-length = 150

# Python 版本
target-version = "py39"

# 排除的文件和目录
exclude = [
    ".git",
    ".venv",
    "__pycache__",
    "build",
    "dist",
    "*.egg-info",
]

[lint]
# 启用的检查规则
select = [
    # Pyflakes
    "F",
    # pycodestyle
    "E",
    "W",
    # isort
    "I",
    # pep8-naming
    "N",
    # pyupgrade
    "UP",
    # flake8-bugbear
    "B",
    # flake8-simplify
    "SIM",
    # flake8-comprehensions
    "C4",
    # flake8-pie
    "PIE",
    # flake8-unused-arguments
    "ARG",
    # flake8-use-pathlib
    "PTH",
]

# 忽略的规则
ignore = [
    # 行长度由格式化工具处理
    "E501",
    # 允许在 __init__.py 中使用 import *
    "F403",
    "F405",
    # 允许抽象方法中的 pass 语句
    "PIE790",
    # 允许函数内部导入
    "E402",
    # 允许UTF-8编码声明（解决IDE中文字符告警）
    "UP009",
    # 忽略导入排序问题（由格式化工具处理）
    "I001",
    # 允许注解函数使用大写开头（Spring Boot风格）
    "N802",
]

# 每个文件的最大复杂度
mccabe = { max-complexity = 10 }

[lint.per-file-ignores]
# 测试文件可以有更宽松的规则
"tests/**/*.py" = [
    "ARG001",  # 测试函数可能有未使用的参数
    "S101",    # 允许使用 assert
]

# __init__.py 文件的特殊规则
"**/__init__.py" = [
    "F401",    # 允许未使用的导入（用于重新导出）
]

[lint.isort]
# 导入排序配置
known-first-party = ["miniboot"]
force-single-line = false
lines-after-imports = 2

[format]
# 格式化配置
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"

# 文档字符串格式化
docstring-code-format = true
docstring-code-line-length = 80
```

### 2. VS Code 配置 (.vscode/settings.json)

```json
{
  "python.formatting.provider": "ruff",
  "python.linting.enabled": true,
  "python.linting.ruffEnabled": true,
  "python.analysis.diagnosticSeverityOverrides": {
    "reportUnnecessaryTypeIgnoreComment": "none",
    "reportUnnecessaryComparison": "none",
    "reportUnnecessaryContains": "none",
    "reportUnnecessaryIsInstance": "none"
  },
  "editor.formatOnSave": true,
  "editor.trimAutoWhitespace": true,
  "files.trimTrailingWhitespace": true,
  "files.insertFinalNewline": true,
  "files.trimFinalNewlines": true
}
```

## 代码检查清单

### 提交前检查

#### 基础代码质量检查

- [ ] 文件头格式正确（包含 shebang、encoding 和文档字符串）
- [ ] 中文注释使用英文标点符号（,.:;!?()[]{}""''）
- [ ] 无中文标点符号（，。：；！？（）【】《》""''）
- [ ] 无语法错误
- [ ] 无尾随空白
- [ ] 无未使用的导入
- [ ] 无未使用的参数
- [ ] 无过于宽泛的异常捕获
- [ ] 所有函数都有类型注解
- [ ] 所有公共函数都有文档字符串
- [ ] 代码格式符合规范
- [ ] 全局变量使用单例模式（使用 SingletonMeta 元类）
- [ ] 无全局实例变量和便捷函数（调用方负责初始化）
- [ ] 单例类正确实现 `_initialized` 检查

#### 项目运行环境检查

- [ ] 所有 Python 执行命令使用 `uv run python`（禁止直接使用 `python`）
- [ ] 脚本文件中的示例命令使用 `uv run python`
- [ ] 文档中的命令示例使用 `uv run python`
- [ ] Git 钩子脚本使用 `uv run python`
- [ ] CI/CD 配置文件使用 `uv run python`

#### AI 代码生成专项检查

**🚨 强制性生成前检查（必须全部完成）：**

- [ ] 已执行现有代码分析（`uv run python scripts/ai_code_validator.py --pre-check`）
- [ ] 已验证目标类/模块的完整结构
- [ ] 已确认所有被调用方法的存在性
- [ ] 已检查接口完整性和类型注解
- [ ] 已获得"ready_for_generation"状态确认

**📝 代码生成质量检查：**

- [ ] AI 生成的代码使用项目标准文件头部格式（包含@author: cz）
- [ ] AI 生成的代码行长度不超过 150 字符
- [ ] AI 生成的代码导入顺序符合规范（标准库 → 第三方 → 本地）
- [ ] AI 生成的代码异常处理具体明确，避免裸露的 except
- [ ] AI 生成的代码变量命名符合 snake_case 规范

**🔍 强制性生成后验证（必须全部通过）：**

- [ ] 已执行代码生成后验证（`uv run python scripts/ai_code_validator.py --post-check`）
- [ ] 已通过依赖关系检查
- [ ] 已通过接口完整性验证
- [ ] 已通过 ruff_helper.py 工具验证
- [ ] 已运行自动修复（ruff check --fix）
- [ ] 已格式化（ruff format）
- [ ] 已获得"validation_passed"状态确认

### 自动化检查

#### 基础代码质量检查

```bash
# 运行完整的代码质量检查
uv run python tests/test_runner.py

# 单独检查中文标点符号
uv run python -m pytest tests/test_code_quality.py::CodeQualityTestCase::test_no_chinese_punctuation -v

# 格式化代码
uv run ruff format .

# 检查代码风格
uv run ruff check .

# 修复可自动修复的问题
uv run ruff check --fix .
```

#### AI 代码生成专用检查

```bash
# 使用AI代码质量助手检查单个文件
uv run python scripts/ruff_helper.py path/to/file.py

# 批量检查AI生成的代码
uv run python scripts/ruff_helper.py miniboot/web/exceptions.py

# AI代码生成完整验证流程
uv run ruff check --fix path/to/generated_file.py && \
uv run ruff format path/to/generated_file.py && \
uv run python scripts/ruff_helper.py path/to/generated_file.py
```

## 强制执行

### 1. Git 钩子

创建 `.git/hooks/pre-commit` 脚本自动检查代码质量

### 2. CI/CD 集成

在发布流程中强制执行代码质量检查

### 3. IDE 配置

配置 VS Code 自动格式化和检查

## 违规处理

- **轻微违规**：自动修复（格式化工具）
- **中等违规**：警告并要求修复
- **严重违规**：阻止提交/发布

## 快速参考

### 常见中文标点符号替换

在编写代码时，请注意以下常见的中文标点符号替换：

```
❌ 中文标点    ✅ 英文标点    说明
，           ,            逗号
。           .            句号
：           :            冒号
；           ;            分号
！           !            感叹号
？           ?            问号
（           (            左圆括号
）           )            右圆括号
【           [            左方括号
】           ]            右方括号
《           <            左尖括号
》           >            右尖括号
"            "            左双引号
"            "            右双引号
'            '            左单引号
'            '            右单引号
```

### 记忆技巧

1. **输入法设置**：将输入法设置为英文标点符号模式
2. **IDE 配置**：配置 IDE 自动检查和提示中文标点符号
3. **代码审查**：在代码审查时重点检查标点符号
4. **自动化工具**：使用 `uv run python tests/test_runner.py` 自动检查

### Python 执行命令快速参考

**🚨 强制要求：所有 Python 执行必须使用 `uv run python`**

```bash
# ✅ 正确的命令格式
uv run python script.py
uv run python -m module_name
uv run python tests/test_runner.py
uv run python scripts/ruff_helper.py file.py

# ❌ 禁止的命令格式
python script.py
python3 script.py
py script.py
./script.py
```

**常用命令示例：**

```bash
# 代码质量检查
uv run python tests/test_runner.py
uv run python scripts/ruff_helper.py path/to/file.py

# 测试运行
uv run python -m pytest tests/
uv run python -m unittest discover

# 脚本执行
uv run python scripts/version.py
uv run python scripts/publish.py
```

### 常见错误示例

```python
# ❌ 错误：混用中文标点
def load_config(self，config_file: str）-> dict：
    """加载配置文件。

    Args：
        config_file： 配置文件路径
    """
    pass

# ✅ 正确：统一使用英文标点
def load_config(self, config_file: str) -> dict:
    """加载配置文件.

    Args:
        config_file: 配置文件路径
    """
    pass
```

遵循这些规范可以确保 Mini-Boot 项目的代码质量始终保持在高水平！
