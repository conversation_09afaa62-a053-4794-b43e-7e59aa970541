#!/usr/bin/env python
"""
* @author: cz
* @description: 默认转换注册器实现
"""

import threading
from collections import deque
from typing import Any, Optional, TypeVar
from weakref import WeakKeyDictionary

from miniboot.errors import ConversionError
from .base import ConversionRegistry, Converter

T = TypeVar("T")


class DefaultConversionRegistry(ConversionRegistry):
    """默认转换注册器实现

    注册和管理多个转换器,按注册顺序查找合适的转换器执行转换.
    使用缓存优化性能,减少不必要的对象创建.
    """

    def __init__(self):
        self._converters: list[Converter] = []
        self._conversion_cache: dict[tuple[type, type], Optional[Converter]] = {}
        self._path_cache: dict[tuple[type, type], Optional[list[type]]] = {}
        self._target_types_cache: WeakKeyDictionary = WeakKeyDictionary()
        self._cache_stats = {
            "conversion_hits": 0,
            "conversion_misses": 0,
            "path_hits": 0,
            "path_misses": 0
        }
        self._lock = threading.RLock()

        # 注册默认转换器
        self._register_default_converters()

    def stats(self) -> dict[str, int]:
        """获取缓存统计信息"""
        return self._cache_stats.copy()

    def clear(self):
        """清空所有缓存"""
        with self._lock:
            self._conversion_cache.clear()
            self._path_cache.clear()
            self._target_types_cache.clear()
            self._cache_stats = {
                "conversion_hits": 0,
                "conversion_misses": 0,
                "path_hits": 0,
                "path_misses": 0
            }



    def can_convert(self, source_type: type, target_type: type) -> bool:
        """检查是否可以进行转换"""
        if source_type == target_type:
            return True

        # 检查缓存
        cache_key = (source_type, target_type)
        if cache_key in self._conversion_cache:
            self._cache_stats["conversion_hits"] += 1
            return self._conversion_cache[cache_key] is not None

        self._cache_stats["conversion_misses"] += 1

        # 检查直接转换
        for converter in self._converters:
            if converter.can_convert(source_type, target_type):
                self._conversion_cache[cache_key] = converter
                return True

        # 检查链式转换
        can_chain_convert = self.can_convert_chain(source_type, target_type)
        if not can_chain_convert:
            self._conversion_cache[cache_key] = None

        return can_chain_convert

    def convert(self, source: Any, target_type: type[T]) -> T:
        """执行类型转换"""
        if source is None:
            return None

        source_type = type(source)
        if source_type == target_type:
            return source

        # 检查缓存
        cache_key = (source_type, target_type)
        if cache_key in self._conversion_cache:
            cached_converter = self._conversion_cache[cache_key]
            if cached_converter:
                self._cache_stats["conversion_hits"] += 1
                try:
                    return cached_converter.convert(source, target_type)
                except Exception as e:
                    raise ConversionError(
                        f"Failed to convert {source} from {source_type.__name__} to {target_type.__name__}: {e}", source_type, target_type
                    ) from e

        self._cache_stats["conversion_misses"] += 1

        # 查找合适的直接转换器(如果缓存中没有)
        for converter in self._converters:
            if converter.can_convert(source_type, target_type):
                # 缓存找到的转换器
                self._conversion_cache[cache_key] = converter
                try:
                    return converter.convert(source, target_type)
                except Exception as e:
                    raise ConversionError(
                        f"Failed to convert {source} from {source_type.__name__} to {target_type.__name__}: {e}", source_type, target_type
                    ) from e

        # 尝试链式转换
        try:
            return self.convert_chain(source, target_type)
        except ConversionError:
            # 如果链式转换也失败,抛出原始错误
            raise ConversionError(f"No converter found for {source_type.__name__} to {target_type.__name__}", source_type, target_type) from None

    def add(self, converter: Converter) -> None:
        """添加转换器"""
        with self._lock:
            if converter not in self._converters:
                self._converters.append(converter)
                # 清空缓存,因为新的转换器可能改变转换能力
                self.clear()

    def remove(self, converter: Converter) -> None:
        """移除转换器"""
        with self._lock:
            if converter in self._converters:
                self._converters.remove(converter)
                # 清空缓存,因为移除转换器可能改变转换能力
                self.clear()

    def converters(self) -> list[Converter]:
        """获取所有转换器的副本"""
        return self._converters.copy()

    def _register_default_converters(self) -> None:
        """注册默认转换器"""
        from .duration import (StrToDurationConverter,
                               StrToFloatCompositeConverter,
                               StrToPercentageConverter, StrToSizeConverter)
        from .strings import (FloatToStringConverter, IntToFloatConverter,
                              ObjectToStringConverter, StrToBoolConverter, StrToFloatConverter,
                              StrToIntConverter, StrToListConverter)

        # 注册字符串转换器
        self.add(StrToBoolConverter())
        self.add(StrToIntConverter())
        self.add(StrToFloatConverter())
        self.add(StrToListConverter())

        # 注册时间间隔和大小转换器
        self.add(StrToDurationConverter())       # 时间间隔转换器 (str -> float)
        self.add(StrToSizeConverter())           # 大小转换器 (str -> int)
        self.add(StrToPercentageConverter())     # 百分比转换器 (str -> float)
        self.add(StrToFloatCompositeConverter()) # 复合浮点转换器 (str -> float)

        # 注册数值转换器(用于链式转换)
        self.add(IntToFloatConverter())
        self.add(FloatToStringConverter())

        # 注册通用转换器
        self.add(ObjectToStringConverter())

    def _find_path(self, source_type: type, target_type: type, max_depth: int) -> Optional[list[type]]:
        """查找转换路径

        使用广度优先搜索算法查找从源类型到目标类型的转换路径.
        使用缓存优化性能.

        Args:
            source_type: 源类型
            target_type: 目标类型
            max_depth: 最大搜索深度

        Returns:
            转换路径列表,如果找不到返回 None
        """
        if source_type == target_type:
            return []

        # 检查路径缓存
        cache_key = (source_type, target_type)
        if cache_key in self._path_cache:
            self._cache_stats["path_hits"] += 1
            cached_path = self._path_cache[cache_key]
            return cached_path.copy() if cached_path is not None else None

        self._cache_stats["path_misses"] += 1

        # 首先检查是否有直接转换
        for converter in self._converters:
            if converter.can_convert(source_type, target_type):
                path = [target_type]
                self._path_cache[cache_key] = path
                return path

        # 如果最大深度为1,且没有直接转换,返回None
        if max_depth <= 1:
            self._path_cache[cache_key] = None
            return None

        # 使用广度优先搜索
        queue = deque([(source_type, [])])
        visited = {source_type}

        while queue:
            current_type, path = queue.popleft()

            # 检查路径长度是否超过最大深度
            if len(path) >= max_depth:
                continue

            # 查找从当前类型可以转换到的所有类型
            for converter in self._converters:
                # 获取转换器支持的目标类型
                target_types = self._target_types(converter, current_type)

                for next_type in target_types:
                    if next_type == target_type:
                        # 找到目标类型,构建并缓存路径
                        result_path = path + [next_type]
                        self._path_cache[cache_key] = result_path.copy()
                        return result_path

                    if next_type not in visited and len(path) + 1 < max_depth:
                        visited.add(next_type)
                        new_path = path + [next_type]
                        queue.append((next_type, new_path))

        # 没有找到路径
        self._path_cache[cache_key] = None
        return None

    def _target_types(self, converter: Converter, source_type: type) -> list[type]:
        """获取转换器从指定源类型可以转换到的目标类型

        Args:
            converter: 转换器
            source_type: 源类型

        Returns:
            目标类型列表
        """
        # 检查缓存
        if converter in self._target_types_cache:
            converter_cache = self._target_types_cache[converter]
            if source_type in converter_cache:
                return converter_cache[source_type]
        else:
            self._target_types_cache[converter] = {}

        # 只检查基本类型,避免过于宽泛的转换
        # 只有当源类型是基本类型时才进行链式转换
        if source_type not in [str, int, float, bool, list]:
            result = []
            self._target_types_cache[converter][source_type] = result
            return result

        # 常见的目标类型列表(限制范围以避免过多的搜索)
        common_types = [str, int, float, bool, list]
        target_types = []

        for target_type in common_types:
            if target_type != source_type and converter.can_convert(source_type, target_type):
                target_types.append(target_type)

        # 缓存结果
        self._target_types_cache[converter][source_type] = target_types
        return target_types
