#!/usr/bin/env python
"""
* @author: cz
* @description: JSON 属性源加载器
"""

import json
from pathlib import Path
from typing import Any, Union

from .base import AbstractPropertySourceLoader


class JsonPropertySourceLoader(AbstractPropertySourceLoader):
    """JSON 属性源加载器

    支持加载 JSON 格式的配置文件.
    """

    def get_file_extensions(self) -> list[str]:
        """获取支持的文件扩展名"""
        return [".json"]

    def _parse_content(self, content: str, location: Union[str, Path]) -> Any:
        """解析JSON内容

        Args:
            content: JSON文件内容
            location: 文件位置(用于错误信息)

        Returns:
            解析后的数据对象

        Raises:
            json.JSONDecodeError: JSON解析失败时抛出
        """
        try:
            return json.loads(content)
        except json.JSONDecodeError as e:
            raise json.JSONDecodeError(f"Failed to parse JSON content from {location}: {e.msg}", e.doc, e.pos) from e
