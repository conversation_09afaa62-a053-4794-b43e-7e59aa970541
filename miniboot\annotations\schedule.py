"""
异步和调度注解实现

提供Mini-Boot框架的异步执行和定时任务注解,包括@Async、@Scheduled、@EnableAsync、@EnableScheduling等.
这些注解用于标记异步执行的方法和定时调度的任务,支持线程池管理和cron表达式.

主要功能:
- Async - 异步执行装饰器
- Scheduled - 定时任务装饰器
- EnableAsync - 启用异步功能装饰器
- EnableScheduling - 启用定时任务装饰器
- ScheduledConfig - 定时任务配置类
"""

import inspect
from datetime import timezone
from typing import Any, Callable, Optional, Union

from .metadata import AsyncMetadata, ScheduledMetadata



# 异步执行注解装饰器
def Async(
    func: Optional[Any] = None, *, pool: Optional[str] = None, timeout: Optional[float] = None
) -> Union[Any, Callable[[Any], Any]]:
    """异步执行注解装饰器

    标记一个方法为异步执行.被标记的方法将在独立的线程池或协程中执行,
    不会阻塞调用方.支持指定线程池名称和超时时间.

    Args:
        func: 被装饰的方法
        pool: 线程池名称,默认使用默认线程池
        timeout: 超时时间(秒),默认无超时

    Returns:
        装饰后的方法或装饰器函数

    Examples:
        @Component
        class EmailService:
            @Async
            async def send_email(self, to: str, subject: str):
                # 异步发送邮件
                pass

            @Async(pool="email-pool", timeout=30.0)
            def send_bulk_emails(self, recipients: list):
                # 在指定线程池中执行,30秒超时
                pass
    """

    def decorator(method: Any) -> Any:
        # 创建异步方法元数据
        metadata = AsyncMetadata(pool=pool)

        # 存储元数据
        method.__async_metadata__ = metadata
        method.__is_async__ = True
        method.__async_pool__ = pool
        method.__async_timeout__ = timeout

        return method

    # 支持无参数调用
    if func is not None:
        return decorator(func)

    return decorator


def EnableAsync(  # noqa: N802
    cls: Optional[Any] = None,
) -> Union[Any, Callable[[Any], Any]]:
    """启用异步功能注解装饰器

    标记一个类启用异步功能.被标记的类中的@Async方法将被异步执行器处理.

    Args:
        cls: 被装饰的类

    Returns:
        装饰后的类或装饰器函数

    Examples:
        @Component
        @EnableAsync
        class EmailService:
            @Async
            async def send_email(self, to: str, subject: str):
                pass
    """

    def decorator(target_cls: Any) -> Any:
        # 标记类启用异步功能
        target_cls.__enable_async__ = True
        target_cls.__is_async_enabled__ = True

        return target_cls

    # 支持无参数调用
    if cls is not None:
        return decorator(cls)

    return decorator



def Scheduled(
    cron: Optional[str] = None,
    fixed_rate: Optional[Union[int, float, str]] = None,
    fixed_delay: Optional[Union[int, float, str]] = None,
    initial_delay: Optional[Union[int, float, str]] = None,
    zone: Optional[Union[str, timezone]] = None,
) -> Callable[[Any], Any]:
    """
    定时任务装饰器

    Args:
        cron: cron表达式,如 "0 */5 * * * *"
        fixed_rate: 固定频率执行,单位秒或时间字符串
        fixed_delay: 固定延迟执行,单位秒或时间字符串
        initial_delay: 初始延迟,单位秒或时间字符串
        zone: 时区设置

    Example:
        @Scheduled(cron="0 */5 * * * *")
        def my_task():
            print("每5分钟执行一次")

        @Scheduled(fixed_rate="30s")
        def another_task():
            print("每30秒执行一次")
    """

    def decorator(func: Any) -> Any:
        # 基本参数验证
        schedule_methods = [cron, fixed_rate, fixed_delay]
        specified_methods = [method for method in schedule_methods if method is not None]

        if not specified_methods:
            raise ValueError("At least one of cron, fixed_rate, or fixed_delay must be specified")

        if len(specified_methods) > 1:
            raise ValueError("Cannot specify multiple scheduling methods")

        # 直接在函数上设置调度参数
        func.__scheduled_cron__ = cron
        func.__scheduled_fixed_rate__ = fixed_rate
        func.__scheduled_fixed_delay__ = fixed_delay
        func.__scheduled_initial_delay__ = initial_delay
        func.__scheduled_zone__ = zone
        func.__is_scheduled__ = True

        return func

    return decorator


def EnableScheduling(cls: Any) -> Any:
    """
    启用定时任务的类装饰器

    标记类启用定时任务功能,通常用于配置类或应用主类

    Example:
        @EnableScheduling
        class MyApplication:
            pass
    """
    # 标记类启用定时任务
    cls.__is_scheduling_enabled__ = True

    return cls


# 工具函数
def is_scheduled(method: Any) -> bool:
    """检查方法是否被@Scheduled装饰"""
    return hasattr(method, "__is_scheduled__") and method.__is_scheduled__





def is_scheduling_enabled(cls: Any) -> bool:
    """检查类是否启用了定时任务"""
    return hasattr(cls, "__is_scheduling_enabled__") and cls.__is_scheduling_enabled__


def is_scheduled_method(method: Any) -> bool:
    """检查方法是否被@Scheduled装饰(别名函数,保持兼容性)"""
    return is_scheduled(method)


def has_scheduled_methods(cls: Any) -> bool:
    """检查类是否包含被@Scheduled装饰的方法"""
    import inspect

    # 获取类的所有方法
    for _name, method in inspect.getmembers(cls, predicate=inspect.ismethod):
        if is_scheduled(method):
            return True

    # 也检查未绑定的函数(静态方法等)
    for _name, func in inspect.getmembers(cls, predicate=inspect.isfunction):
        if is_scheduled(func):
            return True

    # 检查类的所有属性,包括可能的方法
    for name in dir(cls):
        if name.startswith("_"):
            continue
        try:
            attr = getattr(cls, name)
            if callable(attr) and is_scheduled(attr):
                return True
        except (AttributeError, TypeError):
            continue

    return False


# 异步相关工具函数
def is_async(method: Any) -> bool:
    """检查方法是否有@Async注解

    Args:
        method: 要检查的方法

    Returns:
        如果有@Async注解返回True,否则返回False
    """
    return hasattr(method, "__is_async__") and method.__is_async__


def is_async_enabled(cls: Any) -> bool:
    """检查类是否启用了异步功能

    Args:
        cls: 要检查的类

    Returns:
        如果启用了异步功能返回True,否则返回False
    """
    return hasattr(cls, "__is_async_enabled__") and cls.__is_async_enabled__


def get_async_metadata(method: Any) -> Optional[AsyncMetadata]:
    """获取@Async注解元数据

    Args:
        method: 要获取元数据的方法

    Returns:
        Async元数据,如果没有则返回None
    """
    return getattr(method, "__async_metadata__", None)


def get_async_pool(method: Any) -> Optional[str]:
    """获取@Async注解的线程池名称

    Args:
        method: 要获取线程池名称的方法

    Returns:
        线程池名称,如果没有则返回None
    """
    return getattr(method, "__async_pool__", None)


def get_async_timeout(method: Any) -> Optional[float]:
    """获取@Async注解的超时时间

    Args:
        method: 要获取超时时间的方法

    Returns:
        超时时间(秒),如果没有则返回None
    """
    return getattr(method, "__async_timeout__", None)


def get_async_method_info(method: Any) -> dict[str, Any]:
    """获取@Async方法的完整信息

    Args:
        method: 要获取信息的方法

    Returns:
        包含异步方法信息的字典
    """
    if not is_async(method):
        return {}

    return {
        "is_async": True,
        "pool": get_async_pool(method),
        "timeout": get_async_timeout(method),
        "metadata": get_async_metadata(method),
    }


def find_async_methods(cls: Any) -> list[tuple[str, Any]]:
    """查找类中所有的@Async方法

    Args:
        cls: 要查找的类

    Returns:
        (方法名, 方法对象)的列表
    """
    async_methods: list[tuple[str, Any]] = []

    for name, method in inspect.getmembers(cls, predicate=inspect.ismethod):
        if is_async(method):
            async_methods.append((name, method))

    # 也检查未绑定的函数(静态方法等)
    for name, func in inspect.getmembers(cls, predicate=inspect.isfunction):
        if is_async(func):
            async_methods.append((name, func))

    return async_methods


def has_async_methods(cls: Any) -> bool:
    """检查类是否包含@Async方法

    Args:
        cls: 要检查的类

    Returns:
        如果包含@Async方法返回True,否则返回False
    """
    # 检查绑定方法
    if any(is_async(method) for _name, method in inspect.getmembers(cls, predicate=inspect.ismethod)):
        return True

    # 也检查未绑定的函数(静态方法等)
    return any(is_async(func) for _name, func in inspect.getmembers(cls, predicate=inspect.isfunction))


# 调度相关工具函数
def get_metadata(method: Any) -> Optional[ScheduledMetadata]:
    """获取@Scheduled注解元数据

    Args:
        method: 要获取元数据的方法

    Returns:
        Scheduled元数据,如果没有则返回None
    """
    return getattr(method, "__scheduled_metadata__", None)


def get_cron(method: Any) -> Optional[str]:
    """获取@Scheduled注解的cron表达式

    Args:
        method: 要获取cron表达式的方法

    Returns:
        cron表达式,如果没有则返回None
    """
    return getattr(method, "__scheduled_cron__", None)


def get_fixed_rate(method: Any) -> Optional[str]:
    """获取@Scheduled注解的固定频率

    Args:
        method: 要获取固定频率的方法

    Returns:
        固定频率,如果没有则返回None
    """
    return getattr(method, "__scheduled_fixed_rate__", None)


def get_fixed_delay(method: Any) -> Optional[str]:
    """获取@Scheduled注解的固定延迟

    Args:
        method: 要获取固定延迟的方法

    Returns:
        固定延迟,如果没有则返回None
    """
    return getattr(method, "__scheduled_fixed_delay__", None)


def get_method_info(method: Callable[..., Any]) -> dict[str, Any]:
    """获取@Scheduled方法的完整信息

    Args:
        method: 要获取信息的方法

    Returns:
        包含调度方法信息的字典
    """
    if not is_scheduled(method):
        return {}

    return {
        "is_scheduled": True,
        "cron": get_cron(method),
        "fixed_rate": get_fixed_rate(method),
        "fixed_delay": get_fixed_delay(method),
        "metadata": get_metadata(method),
    }


def find_methods(cls: type) -> list[tuple[str, Callable[..., Any]]]:
    """查找类中所有的@Scheduled方法

    Args:
        cls: 要查找的类

    Returns:
        (方法名, 方法对象)的列表
    """
    scheduled_methods: list[tuple[str, Callable[..., Any]]] = []

    for name, method in inspect.getmembers(cls, predicate=inspect.ismethod):
        if is_scheduled(method):
            scheduled_methods.append((name, method))

    # 也检查未绑定的函数(静态方法等)
    for name, func in inspect.getmembers(cls, predicate=inspect.isfunction):
        if is_scheduled(func):
            scheduled_methods.append((name, func))

    return scheduled_methods


def has_methods(cls: type) -> bool:
    """检查类是否包含@Scheduled方法

    Args:
        cls: 要检查的类

    Returns:
        如果包含@Scheduled方法返回True,否则返回False
    """
    # 检查绑定方法
    if any(is_scheduled(method) for _name, method in inspect.getmembers(cls, predicate=inspect.ismethod)):
        return True

    # 也检查未绑定的函数(静态方法等)
    return any(is_scheduled(func) for _name, func in inspect.getmembers(cls, predicate=inspect.isfunction))
