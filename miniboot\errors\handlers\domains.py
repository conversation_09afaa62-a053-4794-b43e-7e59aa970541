#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 域异常处理器

为不同的功能域提供专门的异常处理逻辑。
"""

from typing import Any, Optional

from loguru import logger

from ..domains.bean import (BeanCircularDependencyError, BeanCreationError,
                            BeanDefinitionError, BeanNotFoundError,
                            BeanTypeMismatchError)
from ..domains.context import (ContextConfigurationError, ContextShutdownError,
                               ContextStartupError, ContextStateError)
from ..domains.event import (EventHandlerError, EventPublishError,
                             EventQueueError, EventTypeError)
from ..domains.schedule import (SchedulerNotStartedError, TaskExecutionError,
                                TaskSchedulingError, TaskTimeoutError)
from .base import BaseExceptionHandler


class BeanExceptionHandler(BaseExceptionHandler):
    """Bean异常处理器"""

    def __init__(self):
        super().__init__([
            BeanCreationError, BeanNotFoundError, BeanCircularDependencyError,
            BeanTypeMismatchError, BeanDefinitionError
        ])

    def get_priority(self) -> int:
        return 10  # 高优先级

    def handle(self, exception: Exception, context: Optional[dict[str, Any]] = None) -> Any:
        """处理Bean相关异常"""
        context = context or {}

        # Bean特定的处理逻辑
        if isinstance(exception, BeanCreationError):
            logger.error(f"Bean creation failed: {exception.message}")
            context["recovery_suggestion"] = "Check bean dependencies and configuration"

        elif isinstance(exception, BeanNotFoundError):
            logger.error(f"Bean not found: {exception.message}")
            context["recovery_suggestion"] = "Verify bean name and registration"

        elif isinstance(exception, BeanCircularDependencyError):
            logger.error(f"Circular dependency detected: {exception.message}")
            context["recovery_suggestion"] = "Review bean dependency graph and break circular references"

        elif isinstance(exception, BeanTypeMismatchError):
            logger.error(f"Bean type mismatch: {exception.message}")
            context["recovery_suggestion"] = "Check bean type annotations and casting"

        elif isinstance(exception, BeanDefinitionError):
            logger.error(f"Bean definition error: {exception.message}")
            context["recovery_suggestion"] = "Review bean definition syntax and annotations"

        return super().handle(exception, context)


class ContextExceptionHandler(BaseExceptionHandler):
    """Context异常处理器"""

    def __init__(self):
        super().__init__([
            ContextStartupError, ContextShutdownError, ContextConfigurationError,
            ContextStateError
        ])

    def get_priority(self) -> int:
        return 20  # 中高优先级

    def handle(self, exception: Exception, context: Optional[dict[str, Any]] = None) -> Any:
        """处理Context相关异常"""
        context = context or {}

        # Context特定的处理逻辑
        if isinstance(exception, ContextStartupError):
            logger.error(f"Context startup failed: {exception.message}")
            context["recovery_suggestion"] = "Check application configuration and dependencies"

        elif isinstance(exception, ContextShutdownError):
            logger.error(f"Context shutdown failed: {exception.message}")
            context["recovery_suggestion"] = "Review shutdown hooks and cleanup procedures"

        elif isinstance(exception, ContextConfigurationError):
            logger.error(f"Context configuration error: {exception.message}")
            context["recovery_suggestion"] = "Verify configuration files and environment variables"

        elif isinstance(exception, ContextStateError):
            logger.error(f"Context state error: {exception.message}")
            context["recovery_suggestion"] = "Check context lifecycle and state transitions"

        return super().handle(exception, context)


class EventExceptionHandler(BaseExceptionHandler):
    """Event异常处理器"""

    def __init__(self):
        super().__init__([
            EventPublishError, EventHandlerError, EventQueueError,
            EventTypeError
        ])

    def get_priority(self) -> int:
        return 30  # 中等优先级

    def handle(self, exception: Exception, context: Optional[dict[str, Any]] = None) -> Any:
        """处理Event相关异常"""
        context = context or {}

        # Event特定的处理逻辑
        if isinstance(exception, EventPublishError):
            logger.error(f"Event publish failed: {exception.message}")
            context["recovery_suggestion"] = "Check event bus status and handlers"

        elif isinstance(exception, EventHandlerError):
            logger.error(f"Event handler failed: {exception.message}")
            context["recovery_suggestion"] = "Review event handler implementation and error handling"

        elif isinstance(exception, EventQueueError):
            logger.error(f"Event queue error: {exception.message}")
            context["recovery_suggestion"] = "Check event queue capacity and processing rate"

        elif isinstance(exception, EventTypeError):
            logger.error(f"Event type error: {exception.message}")
            context["recovery_suggestion"] = "Verify event type definitions and serialization"

        return super().handle(exception, context)


class ScheduleExceptionHandler(BaseExceptionHandler):
    """Schedule异常处理器"""

    def __init__(self):
        super().__init__([
            TaskExecutionError, TaskSchedulingError, TaskTimeoutError,
            SchedulerNotStartedError
        ])

    def get_priority(self) -> int:
        return 40  # 中等优先级

    def handle(self, exception: Exception, context: Optional[dict[str, Any]] = None) -> Any:
        """处理Schedule相关异常"""
        context = context or {}

        # Schedule特定的处理逻辑
        if isinstance(exception, TaskExecutionError):
            logger.error(f"Task execution failed: {exception.message}")
            context["recovery_suggestion"] = "Check task implementation and dependencies"

        elif isinstance(exception, TaskSchedulingError):
            logger.error(f"Task scheduling failed: {exception.message}")
            context["recovery_suggestion"] = "Review scheduler configuration and task definitions"

        elif isinstance(exception, TaskTimeoutError):
            logger.error(f"Task timeout: {exception.message}")
            context["recovery_suggestion"] = "Increase task timeout or optimize task performance"

        elif isinstance(exception, SchedulerNotStartedError):
            logger.error(f"Scheduler not started: {exception.message}")
            context["recovery_suggestion"] = "Start the scheduler before scheduling tasks"

        return super().handle(exception, context)


class SystemExceptionHandler(BaseExceptionHandler):
    """系统级异常处理器"""

    def __init__(self):
        from ..base import SystemError
        super().__init__([SystemError])

    def get_priority(self) -> int:
        return 5  # 最高优先级

    def handle(self, exception: Exception, context: Optional[dict[str, Any]] = None) -> Any:
        """处理系统级异常"""
        context = context or {}

        # 系统级异常需要特殊处理
        logger.critical(f"CRITICAL SYSTEM ERROR: {exception}")
        context["severity"] = "CRITICAL"
        context["requires_immediate_attention"] = True
        context["recovery_suggestion"] = "Contact system administrator immediately"

        return super().handle(exception, context)


class ValidationExceptionHandler(BaseExceptionHandler):
    """验证异常处理器"""

    def __init__(self):
        from ..base import ValidationError
        super().__init__([ValidationError])

    def get_priority(self) -> int:
        return 50  # 较低优先级

    def handle(self, exception: Exception, context: Optional[dict[str, Any]] = None) -> Any:
        """处理验证异常"""
        context = context or {}

        # 验证异常通常是用户输入问题
        logger.warning(f"Validation error: {exception}")
        context["severity"] = "LOW"
        context["user_error"] = True
        context["recovery_suggestion"] = "Check input data and correct validation errors"

        return super().handle(exception, context)


def register_default_handlers():
    """注册默认的域异常处理器"""
    from .base import GlobalExceptionCoordinator

    coordinator = GlobalExceptionCoordinator()
    handlers = [
        SystemExceptionHandler(),
        BeanExceptionHandler(),
        ContextExceptionHandler(),
        EventExceptionHandler(),
        ScheduleExceptionHandler(),
        ValidationExceptionHandler(),
    ]

    for handler in handlers:
        coordinator.register_handler(handler)

    logger.info(f"Registered {len(handlers)} default exception handlers")
