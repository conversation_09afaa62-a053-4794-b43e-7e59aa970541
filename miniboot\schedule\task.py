#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 定时任务接口和类型系统 - 实现ScheduledTask接口和任务类型
"""

import asyncio
import inspect
import threading
import uuid
from abc import ABC, abstractmethod
from datetime import datetime, timezone
from enum import Enum
from typing import Any, Callable, Optional

from .config import ScheduledConfig
from ..errors import TaskExecutionError as ScheduleExecutionError
from ..errors import TaskValidationError as TaskRegistrationError


class TaskType(Enum):
    """任务类型枚举"""

    CRON = "cron"
    FIXED_RATE = "fixed_rate"
    FIXED_DELAY = "fixed_delay"
    ONE_TIME = "one_time"


class TaskStatus(Enum):
    """任务状态枚举"""

    CREATED = "created"  # 已创建
    PENDING = "pending"  # 等待执行
    RUNNING = "running"  # 正在执行
    COMPLETED = "completed"  # 执行完成
    FAILED = "failed"  # 执行失败
    CANCELLED = "cancelled"  # 已取消


class TaskIdGenerator:
    """任务ID生成器"""

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._counter = 0
        return cls._instance

    def generate_id(self, prefix: str = "task") -> str:
        """生成唯一的任务ID"""
        with self._lock:
            self._counter += 1
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            return f"{prefix}_{timestamp}_{self._counter:06d}"

    def generate_uuid(self, prefix: str = "task") -> str:
        """生成基于UUID的任务ID"""
        return f"{prefix}_{uuid.uuid4().hex[:8]}"

    @classmethod
    def generate(cls, prefix: str = "task") -> str:
        """类方法:生成唯一的任务ID"""
        instance = cls()
        return instance.generate_id(prefix)


class ScheduledTask(ABC):
    """定时任务抽象基类"""

    def __init__(
        self, task_id: Optional[str] = None, name: Optional[str] = None, config: Optional[ScheduledConfig] = None, description: Optional[str] = None
    ):
        """
        初始化定时任务

        Args:
            task_id: 任务ID,如果为None则自动生成
            name: 任务名称
            config: 定时任务配置
            description: 任务描述
        """
        self.task_id = task_id or TaskIdGenerator().generate_id()
        self.name = name or self.task_id
        self.config = config
        self.description = description
        self.status = TaskStatus.CREATED
        self.created_at = datetime.now(timezone.utc)
        self.last_execution = None
        self.next_execution = None
        self.execution_count = 0
        self.failure_count = 0
        self.last_error = None

        # 确定任务类型
        self.task_type = self._determine_task_type()

        # 默认为同步任务(可被子类覆盖)
        if not hasattr(self, "_is_async_override"):
            self.is_async = False

    def _determine_task_type(self) -> TaskType:
        """确定任务类型"""
        if not self.config:
            return TaskType.ONE_TIME

        if self.config.cron:
            return TaskType.CRON
        elif self.config.fixed_rate:
            return TaskType.FIXED_RATE
        elif self.config.fixed_delay:
            return TaskType.FIXED_DELAY
        else:
            return TaskType.ONE_TIME

    @abstractmethod
    async def execute(self) -> Any:
        """执行任务 - 子类必须实现"""
        pass

    def get_task_info(self) -> dict[str, Any]:
        """获取任务信息"""
        return {
            "task_id": self.task_id,
            "name": self.name,
            "task_type": self.task_type.value,
            "status": self.status.value,
            "description": self.description,
            "created_at": self.created_at.isoformat(),
            "last_execution": self.last_execution.isoformat() if self.last_execution else None,
            "next_execution": self.next_execution.isoformat() if self.next_execution else None,
            "execution_count": self.execution_count,
            "failure_count": self.failure_count,
            "last_error": str(self.last_error) if self.last_error else None,
            "config": {
                "cron": self.config.cron if self.config else None,
                "fixed_rate": self.config.fixed_rate if self.config else None,
                "fixed_delay": self.config.fixed_delay if self.config else None,
                "initial_delay": self.config.initial_delay if self.config else None,
            }
            if self.config
            else None,
        }

    def mark_execution_start(self) -> None:
        """标记任务开始执行"""
        self.status = TaskStatus.RUNNING
        execution_time = datetime.now(timezone.utc)
        self.last_execution = execution_time
        self.last_execution_time = execution_time  # 保持兼容性
        self.execution_count += 1

    def mark_execution_success(self) -> None:
        """标记任务执行成功"""
        self.status = TaskStatus.COMPLETED
        self.last_error = None

    def mark_execution_failure(self, error: Exception) -> None:
        """标记任务执行失败"""
        self.status = TaskStatus.FAILED
        self.failure_count += 1
        self.last_error = error

    def mark_cancelled(self) -> None:
        """标记任务已取消"""
        self.status = TaskStatus.CANCELLED

    def reset_status(self) -> None:
        """重置任务状态为等待执行"""
        self.status = TaskStatus.PENDING

    def __str__(self) -> str:
        return f"ScheduledTask(id={self.task_id}, name={self.name}, type={self.task_type.value})"

    def __repr__(self) -> str:
        return self.__str__()


class SimpleTask(ScheduledTask):
    """简单任务实现(用于测试)"""

    def __init__(self, func, config, name, task_type):
        """兼容测试的构造函数"""
        self.func = func
        self._is_async_override = True  # 标记使用自定义is_async逻辑
        super().__init__(name=name, config=config)
        self.task_type = task_type
        # 设置正确的is_async值
        self.is_async = asyncio.iscoroutinefunction(self.func)

    async def execute(self):
        """执行任务"""
        from datetime import datetime, timezone

        self.last_execution_time = datetime.now(timezone.utc)
        self.mark_execution_start()

        try:
            if asyncio.iscoroutinefunction(self.func):
                result = await self.func()
            else:
                result = self.func()

            self.mark_execution_success()
            return result
        except Exception as e:
            self.mark_execution_failure(e)
            raise


class MethodTask(ScheduledTask):
    """Bean方法任务"""

    def __init__(
        self,
        method: Callable,
        instance: Optional[Any] = None,
        task_id: Optional[str] = None,
        name: Optional[str] = None,
        config: Optional[ScheduledConfig] = None,
        description: Optional[str] = None,
    ):
        """
        初始化方法任务

        Args:
            method: 要执行的方法
            instance: 方法所属的实例(如果是实例方法)
            task_id: 任务ID
            name: 任务名称
            config: 定时任务配置
            description: 任务描述
        """
        # 验证方法
        if not callable(method):
            raise TaskRegistrationError(f"方法必须是可调用的: {method}", task_id=task_id or "unknown", task_method=str(method))

        # 生成默认名称
        if not name:
            name = f"{instance.__class__.__name__}.{method.__name__}" if instance else method.__name__

        super().__init__(task_id, name, config, description)

        self.method = method
        self.instance = instance
        # 检查是否为异步方法
        self.is_async = asyncio.iscoroutinefunction(method)
        self.is_bound_method = instance is not None

        # 进一步验证方法
        self._validate_method()

    def _validate_method(self) -> None:
        """验证方法的有效性"""

        # 检查方法签名
        sig = inspect.signature(self.method)
        params = list(sig.parameters.values())

        # 如果是绑定方法,第一个参数应该是self
        if self.is_bound_method and params and params[0].name == "self":
            params = params[1:]  # 跳过self参数

        # 定时任务方法不应该有必需的参数
        required_params = [p for p in params if p.default == inspect.Parameter.empty]
        if required_params:
            raise TaskRegistrationError(
                f"定时任务方法不能有必需的参数: {[p.name for p in required_params]}", task_id=self.task_id, task_method=str(self.method)
            )

    async def execute(self) -> Any:
        """执行方法任务"""
        try:
            if self.is_bound_method:
                # 实例方法
                if self.is_async:
                    return await self.method()
                else:
                    return self.method()
            else:
                # 静态方法或函数
                if self.is_async:
                    return await self.method()
                else:
                    return self.method()

        except Exception as e:
            raise ScheduleExecutionError(f"方法任务执行失败: {e}", task_name=self.name, execution_time=datetime.now(timezone.utc).isoformat()) from e




class LambdaTask(ScheduledTask):
    """Lambda函数任务"""

    def __init__(
        self,
        func: Callable,
        task_id: Optional[str] = None,
        name: Optional[str] = None,
        config: Optional[ScheduledConfig] = None,
        description: Optional[str] = None,
        args: tuple = (),
        kwargs: Optional[dict[str, Any]] = None,
    ):
        """
        初始化Lambda任务

        Args:
            func: 要执行的函数
            task_id: 任务ID
            name: 任务名称
            config: 定时任务配置
            description: 任务描述
            args: 函数参数
            kwargs: 函数关键字参数
        """
        # 生成默认名称
        if not name:
            name = getattr(func, "__name__", "lambda_task")

        super().__init__(task_id, name, config, description)

        self.func = func
        self.args = args or ()
        self.kwargs = kwargs or {}
        self.is_async = asyncio.iscoroutinefunction(func)

        # 验证函数
        self._validate_function()

    def _validate_function(self) -> None:
        """验证函数的有效性"""
        if not callable(self.func):
            raise TaskRegistrationError(f"函数必须是可调用的: {self.func}", task_id=self.task_id, task_method=str(self.func))

    async def execute(self) -> Any:
        """执行Lambda任务"""
        try:
            if self.is_async:
                return await self.func(*self.args, **self.kwargs)
            else:
                return self.func(*self.args, **self.kwargs)

        except Exception as e:
            raise ScheduleExecutionError(
                f"Lambda任务执行失败: {e}", task_name=self.name, execution_time=datetime.now(timezone.utc).isoformat()
            ) from e




class TaskFactory:
    """任务工厂类"""

    @staticmethod
    def create_method(
        method: Callable,
        instance: Optional[Any] = None,
        config: Optional[ScheduledConfig] = None,
        task_id: Optional[str] = None,
        name: Optional[str] = None,
        description: Optional[str] = None,
    ) -> MethodTask:
        """创建方法任务"""
        return MethodTask(method=method, instance=instance, task_id=task_id, name=name, config=config, description=description)

    @staticmethod
    def create_lambda(
        func: Callable,
        config: Optional[ScheduledConfig] = None,
        task_id: Optional[str] = None,
        name: Optional[str] = None,
        description: Optional[str] = None,
        args: tuple = (),
        kwargs: Optional[dict[str, Any]] = None,
    ) -> LambdaTask:
        """创建Lambda任务"""
        return LambdaTask(func=func, task_id=task_id, name=name, config=config, description=description, args=args, kwargs=kwargs)

    @staticmethod
    def from_method(method: Any, instance: Optional[Any] = None) -> Optional[MethodTask]:
        """从带@Scheduled装饰的方法创建任务"""
        from ..annotations.schedule import is_scheduled

        if not is_scheduled(method):
            return None

        # 直接从方法属性创建配置
        config = ScheduledConfig(
            cron=getattr(method, "__scheduled_cron__", None),
            fixed_rate=getattr(method, "__scheduled_fixed_rate__", None),
            fixed_delay=getattr(method, "__scheduled_fixed_delay__", None),
            initial_delay=getattr(method, "__scheduled_initial_delay__", None),
            zone=getattr(method, "__scheduled_zone__", None),
        )

        return TaskFactory.create_method(method=method, instance=instance, config=config)


class TaskRegistry:
    """任务注册表"""

    def __init__(self):
        self._tasks: dict[str, ScheduledTask] = {}
        self._lock = threading.RLock()

    def register_task(self, task: ScheduledTask) -> str:
        """注册任务"""
        with self._lock:
            if task.task_id in self._tasks:
                raise TaskRegistrationError(f"任务ID已存在: {task.task_id}", task_id=task.task_id)

            self._tasks[task.task_id] = task
            return task.task_id



    def unregister_task(self, task_id: str) -> bool:
        """注销任务"""
        with self._lock:
            if task_id in self._tasks:
                del self._tasks[task_id]
                return True
            return False

    def get_task(self, task_id: str) -> Optional[ScheduledTask]:
        """获取任务"""
        with self._lock:
            return self._tasks.get(task_id)

    def get_all_tasks(self) -> dict[str, ScheduledTask]:
        """获取所有任务"""
        with self._lock:
            return self._tasks.copy()

    def get_tasks_by_type(self, task_type: TaskType) -> dict[str, ScheduledTask]:
        """根据类型获取任务"""
        with self._lock:
            return {task_id: task for task_id, task in self._tasks.items() if task.task_type == task_type}

    def get_tasks_by_status(self, status: TaskStatus) -> dict[str, ScheduledTask]:
        """根据状态获取任务"""
        with self._lock:
            return {task_id: task for task_id, task in self._tasks.items() if task.status == status}

    def clear(self) -> None:
        """清空所有任务"""
        with self._lock:
            self._tasks.clear()

    def get_stats(self) -> dict[str, Any]:
        """获取统计信息"""
        return self.get_statistics()

    def get_statistics(self) -> dict[str, Any]:
        """获取统计信息(别名方法)"""
        with self._lock:
            total_tasks = len(self._tasks)

            # 按类型统计
            type_stats = {}
            for task_type in TaskType:
                count = len(self.get_tasks_by_type(task_type))
                if count > 0:  # 只包含有任务的类型
                    type_stats[task_type] = count

            # 按状态统计
            status_stats = {}
            for status in TaskStatus:
                count = len(self.get_tasks_by_status(status))
                if count > 0:  # 只包含有任务的状态
                    status_stats[status] = count

            return {
                "total_tasks": total_tasks,
                "tasks_by_type": type_stats,
                "tasks_by_status": status_stats,
                # 向后兼容的字段名
                "by_type": {task_type.value: count for task_type, count in type_stats.items()},
                "by_status": {status.value: count for status, count in status_stats.items()},
            }
