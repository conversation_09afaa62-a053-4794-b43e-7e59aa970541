#!/usr/bin/env python
"""
* @author: cz
* @description: 资源加载器接口定义
"""

from abc import ABC, abstractmethod
from pathlib import Path
from typing import Union

from miniboot.errors import  ResourceLoadError, ResourceNotFoundError

from ..sources import PropertySource


class ResourceLoader(ABC):
    """资源加载器接口

    定义统一的资源加载接口,支持从不同来源加载资源.
    """

    @abstractmethod
    def exists(self, location: Union[str, Path]) -> bool:
        """检查资源是否存在

        Args:
            location: 资源位置

        Returns:
            如果资源存在返回 True,否则返回 False
        """
        pass

    @abstractmethod
    def load(self, location: Union[str, Path]) -> str:
        """加载资源内容

        Args:
            location: 资源位置

        Returns:
            资源内容字符串

        Raises:
            ResourceNotFoundError: 资源不存在时抛出
            ResourceLoadError: 资源加载失败时抛出
        """
        pass


class PropertySourceLoader(ABC):
    """属性源加载器接口

    定义从特定格式的配置文件加载属性源的接口.
    """

    @abstractmethod
    def get_file_extensions(self) -> list[str]:
        """获取支持的文件扩展名

        Returns:
            支持的文件扩展名列表
        """
        pass

    @abstractmethod
    def load(self, name: str, resource_loader: ResourceLoader, location: Union[str, Path]) -> list[PropertySource]:
        """加载属性源

        Args:
            name: 属性源名称
            resource_loader: 资源加载器
            location: 资源位置

        Returns:
            属性源列表

        Raises:
            PropertySourceLoadError: 属性源加载失败时抛出
        """
        pass


class DefaultResourceLoader(ResourceLoader):
    """默认资源加载器实现

    支持从文件系统加载资源.
    """

    def exists(self, location: Union[str, Path]) -> bool:
        """检查文件是否存在"""
        path = Path(location)
        return path.exists() and path.is_file()

    def load(self, location: Union[str, Path]) -> str:
        """从文件系统加载资源"""
        path = Path(location)

        if not self.exists(path):
            raise ResourceNotFoundError(path)

        try:
            with path.open("r", encoding="utf-8") as f:
                return f.read()
        except Exception as e:
            raise ResourceLoadError(path, e) from e
