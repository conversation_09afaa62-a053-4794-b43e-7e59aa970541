#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 定时任务模块 - 提供Spring Boot风格的定时任务功能

主要功能:
- @Scheduled 注解 - 定时任务注解和配置
- @EnableScheduling 注解 - 启用定时任务功能
- 任务调度器 (TaskScheduler) - 基于 APScheduler 的任务调度器
- 任务管理 (TaskManager) - 任务的启动、停止、监控功能
- Cron 表达式支持和验证
"""

from .config import ScheduledConfig
from ..errors import CronExpressionError
from ..errors import SchedulerConfigurationError as ScheduleConfigurationError
from ..errors import SchedulerNotStartedError
from ..errors import TaskExecutionError as ScheduleError
from ..errors import TaskExecutionError as ScheduleException
from ..errors import TaskExecutionError as ScheduleExecutionError
from ..errors import TaskValidationError as TaskRegistrationError
from .manager import (FixedDelayTaskHandler, TaskExecutionMetrics, TaskManager,
                      TaskWrapper)
from .properties import (CoalescePolicy, ConcurrencyConfig, ExecutorConfig,
                         ExecutorType, JobStoreConfig, JobStoreType,
                         MisfireGracePolicy, SchedulerConfigFactory,
                         SchedulerConfigurationProperties, SchedulerProperties,
                         SchedulerPropertiesBuilder, TriggerConfig)
from .scheduler import MiniBootScheduler, SchedulerState
from .task import (LambdaTask, MethodTask, ScheduledTask, SimpleTask,
                   TaskFactory, TaskIdGenerator, TaskRegistry, TaskStatus,
                   TaskType)

__all__ = [
    # 配置类
    "ScheduledConfig",
    # 任务类型和状态
    "TaskType",
    "TaskStatus",
    # 任务类
    "ScheduledTask",
    "SimpleTask",
    "MethodTask",
    "LambdaTask",
    # 工具类
    "TaskIdGenerator",
    "TaskFactory",
    "TaskRegistry",
    # 调度器
    "MiniBootScheduler",
    "SchedulerState",
    # 任务管理
    "TaskManager",
    "TaskWrapper",
    "TaskExecutionMetrics",
    "FixedDelayTaskHandler",
    # 配置系统
    "SchedulerProperties",
    "SchedulerPropertiesBuilder",
    "SchedulerConfigFactory",
    "ConcurrencyConfig",
    "JobStoreConfig",
    "ExecutorConfig",
    "TriggerConfig",
    "JobStoreType",
    "ExecutorType",
    "CoalescePolicy",
    "MisfireGracePolicy",
    # 配置属性绑定
    "SchedulerConfigurationProperties",
    # 异常类
    "ScheduleException",
    "ScheduleConfigurationError",
    "CronExpressionError",
    "ScheduleExecutionError",
    "SchedulerNotStartedError",
    "TaskRegistrationError",
    "DurationParseError",
]
