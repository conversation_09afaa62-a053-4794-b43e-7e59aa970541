#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 配置优先级管理器 - 提供统一的配置优先级定义和管理功能
"""

from enum import IntEnum


class ConfigurationPriority(IntEnum):
    """配置优先级枚举

    定义了Mini-Boot框架中所有配置源的优先级层次,数值越大优先级越高.
    参考Spring Boot的配置优先级设计,确保用户配置能够正确覆盖框架默认配置.

    优先级层次(从高到低):
    1. 命令行参数 (10000) - 最高优先级,运行时指定
    2. 系统环境变量 (9000) - 系统级配置
    3. 用户Profile特定配置 (8000) - 用户环境特定配置
    4. 用户默认配置 (7000) - 用户项目配置
    5. 框架Profile特定配置 (6000) - 框架环境特定配置
    6. 框架默认配置 (5000) - 框架内置配置
    7. 系统默认值 (1000) - 最低优先级
    """

    # 命令行参数 - 最高优先级
    COMMAND_LINE = 10000

    # 系统环境变量
    SYSTEM_ENVIRONMENT = 9000

    # 用户配置 - 高优先级
    USER_PROFILE_SPECIFIC = 8000  # application-{profile}.yml in user project
    USER_DEFAULT = 7000  # application.yml in user project

    # 框架配置 - 低优先级
    FRAMEWORK_PROFILE_SPECIFIC = 6000  # application-{profile}.yml in framework
    FRAMEWORK_DEFAULT = 5000  # application.yml in framework

    # 系统默认值 - 最低优先级
    SYSTEM_DEFAULT = 1000


class ConfigurationLayer:
    """配置层定义

    定义配置的逻辑分层,用于区分不同来源的配置.
    """

    # 用户层配置
    USER = "user"

    # 框架层配置
    FRAMEWORK = "framework"

    # 系统层配置
    SYSTEM = "system"


class ConfigurationSearchPath:
    """配置文件搜索路径管理器

    管理不同层次配置文件的搜索路径,确保用户配置优先于框架配置.
    根据运行环境自动调整路径分类.
    """

    def __init__(self):
        """初始化搜索路径管理器"""
        # 检测当前运行环境
        self._environment_type = self._detect_environment_type()

        # 根据环境类型初始化路径
        self._init_paths_by_environment()

        # 支持环境变量指定的配置路径
        self._load_env_paths()

    def _detect_environment_type(self) -> str:
        """检测当前环境类型

        Returns:
            环境类型: 'framework_development' 或 'application_development'
        """
        from pathlib import Path

        # 方法1：检查miniboot模块位置
        try:
            import miniboot
            miniboot_path = Path(miniboot.__file__).parent.absolute()
            current_path = Path.cwd().absolute()

            # 如果 miniboot 模块就在当前目录下，说明是框架开发环境
            if miniboot_path.parent == current_path:
                return "framework_development"
        except Exception:
            pass

        # 方法2：检查目录结构特征（备用方案）
        framework_dirs = ["miniboot", "tests", "samples", "scripts"]
        existing_dirs = sum(1 for d in framework_dirs if Path(d).is_dir())

        if existing_dirs >= 3:
            return "framework_development"

        # 默认为应用开发环境
        return "application_development"

    def _init_paths_by_environment(self):
        """根据环境类型初始化路径"""
        if self._environment_type == "framework_development":
            # 框架开发环境：./resources 是框架配置
            self._user_paths: list[str] = [
                ".",  # 当前目录
                "./config",  # config子目录
                "./src/main/resources",  # Maven标准目录
                "./conf",  # 常见的配置目录
                "./settings",  # 设置目录
                "./cfg",  # 配置目录简写
                "./etc",  # Unix风格配置目录
            ]

            self._framework_paths: list[str] = [
                "./resources",  # 框架资源目录（当前项目就是框架）
                "miniboot/resources",  # 安装包中的框架资源目录
                "miniboot/config",  # 框架配置目录
            ]
        else:
            # 应用开发环境：./resources 是用户配置
            self._user_paths: list[str] = [
                ".",  # 当前目录
                "./config",  # config子目录
                "./resources",  # resources目录（用户应用配置）
                "./src/main/resources",  # Maven标准目录
                "./conf",  # 常见的配置目录
                "./settings",  # 设置目录
                "./cfg",  # 配置目录简写
                "./etc",  # Unix风格配置目录
            ]

            self._framework_paths: list[str] = [
                "miniboot/resources",  # 框架资源目录
                "miniboot/config",  # 框架配置目录
            ]

        self._system_paths: list[str] = [
            "/etc/miniboot",  # 系统配置目录
            "~/.miniboot",  # 用户主目录配置
        ]

        # 基础用户路径(不包含环境变量路径)
        self._base_user_paths = self._user_paths.copy()

    def _load_env_paths(self) -> None:
        """从环境变量加载额外的配置路径"""
        import os

        # 重置为基础路径
        self._user_paths = self._base_user_paths.copy()

        # 支持 MINIBOOT_CONFIG_PATH 环境变量
        config_path = os.getenv("MINIBOOT_CONFIG_PATH")
        if config_path:
            # 支持多个路径,用冒号或分号分隔
            separator = ";" if os.name == "nt" else ":"
            paths = config_path.split(separator)
            for path in reversed(paths):  # 反向插入,保持顺序
                path = path.strip()
                if path:
                    self._user_paths.insert(0, path)  # 环境变量路径优先级最高

        # 支持 MINIBOOT_HOME 环境变量
        miniboot_home = os.getenv("MINIBOOT_HOME")
        if miniboot_home:
            from pathlib import Path
            home_config_path = str(Path(miniboot_home) / "config")
            self._user_paths.insert(0, home_config_path)

    def get_user_paths(self) -> list[str]:
        """获取用户配置搜索路径"""
        # 每次获取时重新加载环境变量路径,确保动态更新
        self._load_env_paths()
        return self._user_paths.copy()

    def get_framework_paths(self) -> list[str]:
        """获取框架配置搜索路径"""
        return self._framework_paths.copy()

    def get_system_paths(self) -> list[str]:
        """获取系统配置搜索路径"""
        return self._system_paths.copy()

    def get_all_paths(self) -> list[str]:
        """获取所有搜索路径,按优先级排序"""
        return self._user_paths + self._framework_paths + self._system_paths

    def get_environment_type(self) -> str:
        """获取当前环境类型

        Returns:
            环境类型: 'framework_development' 或 'application_development'
        """
        return self._environment_type

    def add_user_path(self, path: str, first: bool = True) -> None:
        """添加用户配置路径

        Args:
            path: 配置路径
            first: 是否添加到最前面(最高优先级)
        """
        if path not in self._user_paths:
            if first:
                self._user_paths.insert(0, path)
            else:
                self._user_paths.append(path)

    def add_framework_path(self, path: str, first: bool = True) -> None:
        """添加框架配置路径

        Args:
            path: 配置路径
            first: 是否添加到最前面(最高优先级)
        """
        if path not in self._framework_paths:
            if first:
                self._framework_paths.insert(0, path)
            else:
                self._framework_paths.append(path)



class ConfigurationPriorityManager:
    """配置优先级管理器

    统一管理配置优先级、搜索路径和合并策略.
    """

    def __init__(self):
        """初始化优先级管理器"""
        self._search_path = ConfigurationSearchPath()

    def get_priority_for_source(self, source_name: str, layer: str, is_profile_specific: bool = False) -> int:
        """获取配置源的优先级

        Args:
            source_name: 配置源名称
            layer: 配置层(user/framework/system)
            is_profile_specific: 是否为Profile特定配置

        Returns:
            配置优先级数值
        """
        if source_name == "commandLine":
            return ConfigurationPriority.COMMAND_LINE
        elif source_name == "systemEnvironment":
            return ConfigurationPriority.SYSTEM_ENVIRONMENT
        elif layer == ConfigurationLayer.USER:
            return ConfigurationPriority.USER_PROFILE_SPECIFIC if is_profile_specific else ConfigurationPriority.USER_DEFAULT
        elif layer == ConfigurationLayer.FRAMEWORK:
            return ConfigurationPriority.FRAMEWORK_PROFILE_SPECIFIC if is_profile_specific else ConfigurationPriority.FRAMEWORK_DEFAULT
        else:
            return ConfigurationPriority.SYSTEM_DEFAULT



    def get_search_path(self) -> ConfigurationSearchPath:
        """获取搜索路径管理器"""
        return self._search_path

    def set_merge_strategy(self, property_key: str, strategy: str) -> None:
        """设置属性的合并策略

        Args:
            property_key: 属性键(支持*通配符)
            strategy: 合并策略
        """
        self._merge_strategies[property_key] = strategy

    def is_user_configuration_higher_priority(self, user_priority: int, framework_priority: int) -> bool:
        """判断用户配置是否比框架配置优先级更高

        Args:
            user_priority: 用户配置优先级
            framework_priority: 框架配置优先级

        Returns:
            True if user configuration has higher priority
        """
        return user_priority > framework_priority

    def validate_priority_order(self, priorities: list[int]) -> bool:
        """验证优先级顺序是否正确

        Args:
            priorities: 优先级列表

        Returns:
            True if priorities are in correct order (descending)
        """
        return all(priorities[i] >= priorities[i + 1] for i in range(len(priorities) - 1))


# 全局配置优先级管理器实例
_priority_manager = ConfigurationPriorityManager()


def get_priority() -> ConfigurationPriorityManager:
    """获取全局配置优先级管理器实例"""
    return _priority_manager
