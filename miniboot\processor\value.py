#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 值注入处理器 - 实现@Value注解的配置值注入处理

值注入处理器负责处理@Value注解标记的字段和方法,
实现配置值注入功能.支持占位符解析、类型转换和默认值处理.
"""

import inspect
from typing import Any, Optional

from loguru import logger

from ..env.convert import DefaultConversionRegistry
# 环境配置模块是必需依赖
from ..env.environment import StandardEnvironment
from ..errors import BeanCreationError
from ..errors import ProcessorExecutionError as BeanProcessingError
from .base import AbstractAnnotationProcessor, ProcessorOrder
from .cache import cache
from .decorators import processor_exception_handler


def is_value_injection(obj: Any) -> bool:
    """检查对象是否有@Value注解"""
    return hasattr(obj, "__is_value_injection__") and obj.__is_value_injection__


class ValueAnnotationProcessor(AbstractAnnotationProcessor):
    """
    @Value注解处理器

    负责处理@Value注解的配置值注入,支持:
    - 字段注入:直接在字段上使用@Value
    - Setter方法注入:在setter方法上使用@Value
    - 占位符解析:支持${key:default}格式的占位符
    - 类型转换:自动转换配置值到目标类型
    - 默认值处理:支持注解级别和占位符级别的默认值

    处理器在Bean初始化前执行,确保所有配置值在Bean使用前完成注入.

    Example:
        # 字段注入
        class AppConfig:
            @Value("${app.name}")
            app_name: str

        # Setter方法注入
        class DatabaseConfig:
            @Value("${database.port:3306}")
            def set_port(self, port: int):
                self.port = port
    """

    def __init__(self, environment=None):
        """
        初始化值注入处理器

        Args:
            environment: 环境配置实例,用于解析配置值
        """
        super().__init__()  # 调用抽象基类初始化
        self._environment = environment or StandardEnvironment()
        self._conversion_registry = DefaultConversionRegistry()
        self._injection_cache: dict[type, list[tuple]] = {}
        # 获取类型检查缓存
        self._type_cache = cache()

    def set_environment(self, environment) -> None:
        """
        设置环境配置

        Args:
            environment: 环境配置实例
        """
        self._environment = environment

    def _get_annotation_type(self) -> str:
        """获取目标注解类型"""
        return "Value"

    def _process_before(self, bean: Any, bean_name: str) -> Any:
        """前置处理:执行配置值注入"""
        # 执行字段注入
        self._inject_value_fields(bean, bean_name)

        # 执行方法注入
        self._inject_value_methods(bean, bean_name)

        logger.debug(f"Completed value injection for bean: {bean_name}")
        return bean

    def _process_after(self, bean: Any, bean_name: str) -> Any:
        """后置处理:值注入处理器不需要后处理"""
        return bean

    def post_process_before_initialization(self, bean: Any, bean_name: str) -> Any:
        """
        在Bean初始化前处理@Value注解

        使用模板方法模式，委托给抽象基类处理。

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            处理后的Bean实例

        Raises:
            BeanProcessingError: 当配置值注入失败时
        """
        # 委托给抽象基类的模板方法
        return super().post_process_before_initialization(bean, bean_name)

    def post_process_after_initialization(self, bean: Any, bean_name: str) -> Any:
        """
        在Bean初始化后处理(值注入处理器不需要后处理)

        使用模板方法模式，委托给抽象基类处理。

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            原始Bean实例
        """
        # 委托给抽象基类的模板方法
        return super().post_process_after_initialization(bean, bean_name)

    def get_order(self) -> int:
        """
        获取处理器执行顺序

        值注入处理器需要在自动装配之后执行,
        确保依赖注入完成后再进行配置值注入.

        Returns:
            执行顺序(数字越小优先级越高)
        """
        return ProcessorOrder.VALUE_INJECTION_PROCESSOR

    def supports(self, bean: Any, _bean_name: str) -> bool:
        """
        检查是否支持处理指定的Bean

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            True表示支持处理此Bean
        """
        if bean is None:
            return False

        # 检查Bean类是否有@Value注解
        return self._has_value_annotations(bean.__class__)

    def _inject_value_fields(self, bean: Any, bean_name: str) -> None:
        """
        注入@Value标记的字段

        Args:
            bean: Bean实例
            bean_name: Bean名称
        """
        bean_class = bean.__class__

        # 获取缓存的字段信息
        field_info = self._get_value_fields(bean_class)

        for field_name, field_type, value_expression, required, default_value in field_info:
            try:
                # 检查字段是否已经有值
                if hasattr(bean, field_name) and getattr(bean, field_name) is not None:
                    continue

                # 解析配置值
                config_value = self._resolve_value(value_expression, field_name, bean_name, required, default_value, field_type)

                if config_value is not None:
                    setattr(bean, field_name, config_value)
                    logger.debug(f"Injected value field '{field_name}' for bean '{bean_name}'")
                elif required:
                    raise BeanCreationError(f"Required value field '{field_name}' could not be resolved", bean_name)

            except Exception as e:
                if required:
                    raise BeanCreationError(f"Failed to inject value field '{field_name}' for bean '{bean_name}'", bean_name, e) from e
                else:
                    logger.warning(f"Optional value field '{field_name}' injection failed: {e}")

    def _inject_value_methods(self, bean: Any, bean_name: str) -> None:
        """
        注入@Value标记的方法

        Args:
            bean: Bean实例
            bean_name: Bean名称
        """
        bean_class = bean.__class__

        # 获取缓存的方法信息
        method_info = self._get_value_methods(bean_class)

        for method_name, method, value_expression, required, default_value in method_info:
            try:
                # 解析方法参数类型
                param_type = self._get_method_parameter_type(method)

                # 解析配置值
                config_value = self._resolve_value(value_expression, method_name, bean_name, required, default_value, param_type)

                if config_value is not None:
                    # 调用方法
                    method(bean, config_value)
                    logger.debug(f"Injected value method '{method_name}' for bean '{bean_name}'")
                elif required:
                    raise BeanCreationError(f"Required value method '{method_name}' could not be resolved", bean_name)

            except Exception as e:
                if required:
                    raise BeanCreationError(f"Failed to inject value method '{method_name}' for bean '{bean_name}'", bean_name, e) from e
                else:
                    logger.warning(f"Optional value method '{method_name}' injection failed: {e}")

    def _get_value_fields(self, bean_class: type) -> list[tuple]:
        """
        获取类中的@Value字段信息

        Args:
            bean_class: Bean类

        Returns:
            字段信息列表:[(field_name, field_type, value_expression, required, default_value), ...]
        """
        if bean_class in self._injection_cache:
            return self._injection_cache[bean_class]

        fields = []

        # 检查字段级别的@Value注解
        # 使用缓存获取类属性列表
        attributes = self._type_cache.get_class_attributes(bean_class)
        # 使用缓存获取类型提示
        type_hints = self._type_cache.get_type_hints(bean_class)

        for attr_name in attributes:
            try:
                attr = self._type_cache.get_attribute_safely(bean_class, attr_name)
                if attr and is_value_injection(attr) and hasattr(attr, "__is_field_value_injection__"):
                    # 获取字段类型
                    field_type = type_hints.get(attr_name)

                    if field_type:
                        # 获取@Value元数据
                        metadata = getattr(attr, "__value_metadata__", None)
                        if metadata:
                            fields.append((attr_name, field_type, metadata.value, metadata.required, metadata.default_value))
            except Exception:
                continue

        # 缓存结果
        self._injection_cache[bean_class] = fields
        return fields

    def _get_value_methods(self, bean_class: type) -> list[tuple]:
        """
        获取类中的@Value方法信息

        Args:
            bean_class: Bean类

        Returns:
            方法信息列表:[(method_name, method, value_expression, required, default_value), ...]
        """
        methods = []

        # 使用缓存获取类属性列表
        attributes = self._type_cache.get_class_attributes(bean_class)

        for attr_name in attributes:
            try:
                attr = self._type_cache.get_attribute_safely(bean_class, attr_name)

                if (
                    attr
                    and self._type_cache.is_method_or_function(attr)
                    and is_value_injection(attr)
                    and hasattr(attr, "__is_setter_value_injection__")
                ):
                    # 获取@Value元数据
                    metadata = getattr(attr, "__value_metadata__", None)
                    if metadata:
                        methods.append((attr_name, attr, metadata.value, metadata.required, metadata.default_value))
            except Exception:
                continue

        return methods

    def _has_value_annotations(self, bean_class: type) -> bool:
        """
        检查类是否有@Value注解

        Args:
            bean_class: Bean类

        Returns:
            True表示有@Value注解
        """
        # 检查字段和方法级别的@Value注解
        for attr_name in dir(bean_class):
            if attr_name.startswith("_"):
                continue

            try:
                attr = getattr(bean_class, attr_name)
                if is_value_injection(attr):
                    return True
            except Exception:
                continue

        return False

    def _resolve_value(self, value_expression: str, field_name: str, bean_name: str, required: bool, default_value: Any, target_type: type) -> Any:
        """
        解析配置值

        Args:
            value_expression: 值表达式(如${app.name:default})
            field_name: 字段名称
            bean_name: Bean名称
            required: 是否必需
            default_value: 默认值
            target_type: 目标类型

        Returns:
            解析的配置值
        """
        if self._environment is None:
            if required:
                raise BeanCreationError("Environment not available for value resolution", bean_name)
            return default_value

        try:
            # 解析占位符
            resolved_value = self._environment.resolve_placeholders(value_expression)

            # 如果解析后的值与原表达式相同,说明占位符未被解析
            if resolved_value == value_expression and value_expression.startswith("${"):
                if default_value is not None:
                    resolved_value = default_value
                elif required:
                    raise BeanCreationError(f"Required property '{value_expression}' could not be resolved", bean_name)
                else:
                    return None

            # 类型转换
            if resolved_value is not None and target_type:
                return self._convert_value(resolved_value, target_type, field_name, bean_name)

            return resolved_value

        except Exception as e:
            if required:
                raise BeanCreationError(f"Failed to resolve value '{value_expression}' for field '{field_name}'", bean_name, e) from e
            return default_value

    def _convert_value(self, value: Any, target_type: type, field_name: str, bean_name: str) -> Any:
        """
        转换值到目标类型

        Args:
            value: 原始值
            target_type: 目标类型
            field_name: 字段名称
            bean_name: Bean名称

        Returns:
            转换后的值
        """
        if value is None:
            return value

        # 处理字符串形式的类型提示
        if isinstance(target_type, str):
            # 尝试解析字符串类型
            if target_type == "str" or target_type == "<class 'str'>":
                return str(value)
            elif target_type == "int" or target_type == "<class 'int'>":
                return int(value)
            elif target_type == "float" or target_type == "<class 'float'>":
                return float(value)
            elif target_type == "bool" or target_type == "<class 'bool'>":
                return bool(value)
            else:
                # 对于其他字符串类型,直接返回字符串值
                return str(value)

        # 简单的类型检查和转换
        try:
            if isinstance(value, target_type):
                return value
        except TypeError:
            # 如果target_type不是有效类型,跳过检查
            pass

        try:
            # 使用转换注册器
            if self._conversion_registry and self._conversion_registry.can_convert(type(value), target_type):
                return self._conversion_registry.convert(value, target_type)

            # 回退到直接转换
            return target_type(value)

        except Exception as e:
            raise BeanCreationError(f"Failed to convert value '{value}' to type {target_type.__name__} for field '{field_name}'", bean_name, e) from e

    def _get_method_parameter_type(self, method) -> Optional[type]:
        """
        获取方法参数类型

        Args:
            method: 方法对象

        Returns:
            参数类型
        """
        try:
            # 获取方法签名
            sig = inspect.signature(method)

            # 跳过self参数
            parameters = list(sig.parameters.values())[1:]

            if parameters:
                param = parameters[0]
                return param.annotation if param.annotation != inspect.Parameter.empty else str

            return str

        except Exception:
            return str
