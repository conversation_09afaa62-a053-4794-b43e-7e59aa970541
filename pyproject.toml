[project]
name = "miniboot"
version = "0.0.4"
description = "轻量级 Python Web 框架，提供类似 Spring Boot 的开发体验"
readme = "README.md"
license = { file = "LICENSE" }
authors = [
    { name = "Mini-Boot Team", email = "<EMAIL>" }
]
maintainers = [
    { name = "Mini-Boot Team", email = "<EMAIL>" }
]
keywords = ["web", "framework", "ioc", "dependency-injection", "spring-boot"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    "Topic :: Software Development :: Libraries :: Application Frameworks",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
requires-python = ">=3.9"
dependencies = [
    "apscheduler>=3.11.0",
    "croniter>=6.0.0",
    "fastapi>=0.104.0",
    "httpx>=0.28.1",
    "loguru>=0.7.3",
    "psutil>=7.0.0",
    "pyjwt>=2.10.1",
    "pyttsx3>=2.99",
    "pyyaml>=6.0.2",
    "tqdm>=4.67.1",
    "typing-extensions>=4.14.1",
    "uvicorn[standard]>=0.24.0",
]

# [project.optional-dependencies]
# web = [
#     "fastapi>=0.104.0",
#     "uvicorn[standard]>=0.24.0",
# ]
# redis = [
#     "aioredis>=2.0.0",
# ]
# database = [
#     "sqlalchemy[asyncio]>=2.0.0",
#     "asyncpg>=0.29.0",
# ]
# all = [
#     "miniboot[web,redis,database]",
# ]
# dev = [
#     "pytest>=7.0.0",
#     "pytest-asyncio>=0.21.0",
#     "pytest-cov>=4.0.0",
#     "ruff>=0.1.0",
#     "mypy>=1.7.0",
#     "pre-commit>=3.0.0",
# ]

[project.urls]
Homepage = "https://github.com/miniboot/mini-boot"
Documentation = "https://miniboot.readthedocs.io"
Repository = "https://github.com/miniboot/mini-boot.git"
Issues = "https://github.com/miniboot/mini-boot/issues"
Changelog = "https://github.com/miniboot/mini-boot/blob/main/CHANGELOG.md"

# [project.scripts]
# miniboot = "miniboot.cli:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["miniboot"]

[tool.hatch.build.targets.sdist]
include = [
    "/miniboot",
    "/tests",
    "/docs",
    "/examples",
    "/README.md",
    "/LICENSE",
    "/CHANGELOG.md",
]

# Ruff配置已移至ruff.toml文件中统一管理

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]
"tests/**/*" = ["B011"]

[tool.mypy]
python_version = "3.9"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_return_any = true
strict_equality = true
show_error_codes = true
show_column_numbers = true
pretty = true
color_output = true
error_summary = true
ignore_missing_imports = false
follow_imports = "normal"
namespace_packages = true
explicit_package_bases = true

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false

[tool.pytest.ini_options]
minversion = "0.0.4"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=miniboot",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
    "--cov-fail-under=50",
]
testpaths = ["tests"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "benchmark: marks tests as benchmark tests",
]
asyncio_mode = "auto"

[tool.coverage.run]
source = ["miniboot"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/site-packages/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.coverage.html]
directory = "htmlcov"

[tool.uv]
index-url = "https://mirrors.aliyun.com/pypi/simple/"
extra-index-url = [
    "https://pypi.org/simple/",
]
link-mode = "copy"

[dependency-groups]
dev = [
    "mypy>=1.16.1",
    "pytest>=8.4.1",
    "pytest-asyncio>=1.0.0",
    "pytest-cov>=6.2.1",
    "ruff>=0.12.3",
    "coverage>=7.6.0",
    "aiohttp>=3.12.15",
    "types-croniter>=6.0.0",
    "types-PyYAML>=6.0.12.20250516",
]
