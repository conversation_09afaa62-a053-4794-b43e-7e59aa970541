#!/usr/bin/env python
"""
* @author: cz
* @description: 属性绑定器实现
"""

from dataclasses import MISSING, fields, is_dataclass
from typing import Any, TypeVar, get_origin

from ..convert import ConversionRegistry, DefaultConversionRegistry
from ..resolver import PropertyResolver
from .result import BindingResult

T = TypeVar("T")


def config_properties(prefix: str = "", property_mapping: dict = None):
    """配置属性装饰器

    标记一个类为配置属性类,支持从环境中自动绑定属性.

    Args:
        prefix: 属性前缀
        property_mapping: 属性名映射字典,格式为 {field_name: property_name}
    """

    def decorator(cls):
        cls._config_prefix = prefix
        cls._property_mapping = property_mapping or {}
        return cls

    return decorator


class Binder:
    """属性绑定器

    将环境配置属性绑定到数据类对象.
    """

    def __init__(self, property_resolver: PropertyResolver, conversion_registry: ConversionRegistry = None):
        self._property_resolver = property_resolver
        self._conversion_registry = conversion_registry or DefaultConversionRegistry()

    def bind(self, prefix: str, target_type: type[T]) -> BindingResult:
        """绑定配置属性到目标类型

        Args:
            prefix: 属性前缀
            target_type: 目标类型

        Returns:
            绑定结果
        """
        result = BindingResult()

        try:
            # 检查是否为数据类
            if not is_dataclass(target_type):
                result.add_error(prefix, f"Target type {target_type.__name__} is not a dataclass")
                return result

            # 创建实例
            instance = self._create_instance(prefix, target_type, result)
            result._target = instance

        except Exception as e:
            result.add_error(prefix, f"Failed to bind properties: {e}", e)

        return result

    def _create_instance(self, prefix: str, target_type: type[T], result: BindingResult) -> T:
        """创建并填充实例"""
        field_values = {}

        # 获取数据类字段
        for field in fields(target_type):
            field_name = field.name
            property_key = self._get_property_key(prefix, field_name, target_type)

            try:
                # 获取属性值
                value = self._get_property_value(property_key, field.type, field.default)
                if value is not None:
                    field_values[field_name] = value

            except Exception as e:
                result.add_error(property_key, f"Failed to bind property: {e}", e)

        # 创建实例
        return target_type(**field_values)

    def _get_property_key(self, prefix: str, field_name: str, target_type: type) -> str:
        """获取属性键,支持属性映射

        Args:
            prefix: 属性前缀
            field_name: 字段名
            target_type: 目标类型

        Returns:
            实际的属性键
        """
        # 检查是否有属性映射
        property_mapping = getattr(target_type, "_property_mapping", {})

        # 如果有映射,使用映射的属性名
        mapped_property_name = property_mapping.get(field_name, field_name)

        # 构建完整的属性键
        if prefix:
            return f"{prefix}.{mapped_property_name}"
        else:
            return mapped_property_name

    def _get_property_value(self, key: str, field_type: type, default_value: Any) -> Any:
        """获取并转换属性值"""
        # 检查是否为嵌套数据类
        if is_dataclass(field_type):
            return self._bind_nested_object(key, field_type, default_value)

        # 获取原始值
        raw_value = self._property_resolver.get_property(key)

        if raw_value is None:
            # 使用默认值
            if default_value is not MISSING:
                return default_value
            return None

        # 类型转换
        return self._convert_value(raw_value, field_type)

    def _bind_nested_object(self, prefix: str, target_type: type, default_value: Any) -> Any:
        """绑定嵌套对象

        Args:
            prefix: 属性前缀
            target_type: 目标数据类类型
            default_value: 默认值

        Returns:
            绑定后的嵌套对象实例
        """
        # 检查是否有任何嵌套属性存在
        if not self._has_nested_properties(prefix, target_type):
            # 如果没有嵌套属性,使用默认值
            if default_value is not MISSING:
                return default_value
            return None

        # 递归绑定嵌套对象
        nested_result = self.bind(prefix, target_type)

        if nested_result.has_errors:
            # 如果绑定失败,使用默认值或返回 None
            if default_value is not MISSING:
                return default_value
            return None

        return nested_result.target

    def _has_nested_properties(self, prefix: str, target_type: type = None) -> bool:
        """检查是否存在嵌套属性

        Args:
            prefix: 属性前缀
            target_type: 目标类型,用于检查具体字段

        Returns:
            如果存在以该前缀开头的属性返回 True
        """
        # 如果提供了目标类型,检查其字段对应的属性
        if target_type and is_dataclass(target_type):
            for field in fields(target_type):
                # 使用属性映射获取正确的属性键
                field_key = self._get_property_key(prefix, field.name, target_type)
                if self._property_resolver.contains_property(field_key):
                    return True

                # 如果字段本身也是数据类,递归检查
                if is_dataclass(field.type) and self._has_nested_properties(field_key, field.type):
                    return True

        # 尝试获取一些常见的嵌套属性来判断
        test_keys = [
            f"{prefix}.name",
            f"{prefix}.value",
            f"{prefix}.id",
            f"{prefix}.type",
            f"{prefix}.enabled",
            f"{prefix}.host",
            f"{prefix}.port",
            f"{prefix}.url",
            f"{prefix}.path",
            f"{prefix}.timeout",
        ]

        return any(self._property_resolver.contains_property(test_key) for test_key in test_keys)

    def _convert_value(self, value: Any, target_type: type) -> Any:
        """转换值到目标类型"""
        if value is None:
            return None

        # 处理泛型类型
        origin_type = get_origin(target_type)
        if origin_type is not None:
            target_type = origin_type

        # 如果已经是目标类型
        if isinstance(value, target_type):
            return value

        # 使用转换注册器
        if self._conversion_registry.can_convert(type(value), target_type):
            return self._conversion_registry.convert(value, target_type)

        # 尝试直接转换
        try:
            return target_type(value)
        except Exception as e:
            raise ValueError(f"Cannot convert {value} to {target_type.__name__}") from e
