#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Resource相关异常类

资源加载和管理系统相关的异常。
"""

from pathlib import Path
from typing import Union

from ..base import ApplicationError, ValidationError


# 资源加载相关异常 (ApplicationError)
class ResourceError(ApplicationError):
    """资源错误 - 资源相关的通用错误"""
    max_attempts = 2
    base_delay = 1.0


class ResourceNotFoundError(ValidationError):
    """资源未找到错误 - 指定的资源不存在"""
    # 继承ValidationError的属性：retryable = False

    def __init__(self, location: Union[str, Path]):
        self.location = location
        super().__init__(f"Resource not found: {location}")


class ResourceLoadError(ApplicationError):
    """资源加载错误 - 资源加载失败"""
    max_attempts = 3
    base_delay = 1.0

    def __init__(self, location: Union[str, Path], cause: Exception = None):
        self.location = location
        self.cause = cause
        message = f"Failed to load resource: {location}"
        if cause:
            message += f" - {cause}"
        super().__init__(message)


class PropertySourceLoadError(ApplicationError):
    """属性源加载错误 - 属性源加载失败"""
    max_attempts = 3
    base_delay = 1.0

    def __init__(self, location: Union[str, Path], cause: Exception = None):
        self.location = location
        self.cause = cause
        message = f"Failed to load property source: {location}"
        if cause:
            message += f" - {cause}"
        super().__init__(message)


# 资源访问相关异常 (ApplicationError)
class ResourceAccessError(ApplicationError):
    """资源访问错误 - 资源访问权限或IO错误"""
    max_attempts = 2
    base_delay = 1.0


class ResourcePermissionError(ValidationError):
    """资源权限错误 - 资源访问权限不足"""
    # 继承ValidationError的属性：retryable = False


# 资源格式相关异常 (ValidationError)
class ResourceFormatError(ValidationError):
    """资源格式错误 - 资源格式不正确或不支持"""
    # 继承ValidationError的属性：retryable = False


class ResourceParseError(ValidationError):
    """资源解析错误 - 资源内容解析失败"""
    # 继承ValidationError的属性：retryable = False


# 资源缓存相关异常 (ApplicationError)
class ResourceCacheError(ApplicationError):
    """资源缓存错误 - 资源缓存相关的错误"""
    max_attempts = 2
    base_delay = 0.5


class ResourceCacheExpiredError(ApplicationError):
    """资源缓存过期错误 - 资源缓存已过期"""
    max_attempts = 1
    base_delay = 0.1
