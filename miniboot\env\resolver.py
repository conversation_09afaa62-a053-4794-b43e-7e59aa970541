#!/usr/bin/env python
"""
* @author: cz
* @description: Property resolver module - provides abstract interface for property resolution
"""

import re
from abc import ABC, abstractmethod
from re import Pattern
from typing import Any, TypeVar

T = TypeVar("T")


class PropertyResolver(ABC):
    """属性解析器抽象基类

    定义属性获取和占位符解析的基本接口.
    """

    @abstractmethod
    def get_property(self, key: str, default: Any = None) -> Any:
        """获取属性值

        Args:
            key: 属性键
            default: 默认值

        Returns:
            属性值,如果不存在则返回默认值
        """
        raise NotImplementedError

    @abstractmethod
    def contains_property(self, key: str) -> bool:
        """检查是否包含指定属性

        Args:
            key: 属性键

        Returns:
            如果包含该属性返回 True,否则返回 False
        """
        raise NotImplementedError

    def get_as(self, key: str, target_type: type[T], default: Any = None) -> T:
        """获取指定类型的属性值

        Args:
            key: 属性键
            target_type: 目标类型
            default: 默认值

        Returns:
            转换后的属性值
        """
        value = self.get_property(key, default)
        if value is None:
            return default

        # 如果已经是目标类型,直接返回
        if isinstance(value, target_type):
            return value

        # 使用转换注册器进行类型转换
        try:
            # 延迟导入避免循环依赖
            from .convert import DefaultConversionRegistry

            conversion_registry = DefaultConversionRegistry()

            if conversion_registry.can_convert(type(value), target_type):
                return conversion_registry.convert(value, target_type)
            else:
                # 回退到直接转换
                return target_type(value)
        except Exception as e:
            raise ValueError(f"Cannot convert property '{key}' value '{value}' to type {target_type.__name__}: {e}") from e

    def resolve(self, text: str) -> str:
        """解析占位符

        支持 ${key:default} 格式的占位符解析.

        Args:
            text: 包含占位符的文本

        Returns:
            解析后的文本
        """
        if not text or not isinstance(text, str):
            return text

        # 占位符正则表达式模式: ${key:default} 或 ${key}
        placeholder_pattern: Pattern = re.compile(r"\$\{([^}:]+)(?::([^}]*))?\}")

        return placeholder_pattern.sub(
            lambda match: self._replace_placeholder_with_strategy(match, required=False),
            text
        )

    def _replace_placeholder_with_strategy(self, match, required: bool = False):
        """通用占位符替换逻辑

        Args:
            match: 正则匹配对象
            required: 是否为必需占位符

        Returns:
            替换后的字符串
        """
        key = match.group(1).strip()
        default_value = match.group(2) if match.group(2) is not None else None

        # 获取属性值
        value = self.get_property(key)
        if value is not None:
            return str(value)
        elif default_value is not None:
            return default_value
        elif required:
            raise ValueError(f"Required placeholder '${{{key}}}' not found")
        else:
            # 如果属性未找到且没有默认值,保持原始占位符
            return match.group(0)

    def resolve_required(self, text: str) -> str:
        """解析必需的占位符

        类似于 resolve,但如果占位符无法解析则抛出异常.

        Args:
            text: 包含占位符的文本

        Returns:
            解析后的文本

        Raises:
            ValueError: 如果存在无法解析的占位符
        """
        if not text or not isinstance(text, str):
            return text

        placeholder_pattern: Pattern = re.compile(r"\$\{([^}:]+)(?::([^}]*))?\}")

        try:
            return placeholder_pattern.sub(
                lambda match: self._replace_placeholder_with_strategy(match, required=True),
                text
            )
        except ValueError as e:
            # 重新抛出更具体的错误信息
            raise ValueError(f"Could not resolve placeholder in value '{text}': {e}") from e
