#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Mini-Boot框架异常模块

新的扁平化异常层次结构：
- 四层基础分类：SystemError, ApplicationError, BusinessError, ValidationError
- 扁平化的具体异常类：BeanCreationError, ContextStartupError等
- 内置重试机制：每个异常类包含重试配置属性
- 统一异常处理：全局异常协调器和域特定处理器
- 简化使用方式：@retry装饰器自动读取异常类的重试配置

使用示例：
    from miniboot.errors import BeanCreationError, retry

    @retry
    def create_bean():
        if failed:
            raise BeanCreationError("Bean creation failed")  # 自动重试5次
"""

# 基础异常类和四层分类
from .base import MiniBootException  # 向后兼容别名
from .base import (ApplicationError, BusinessError, MiniBootError, SystemError,
                   ValidationError)

# 标准异常别名
TimeoutError = TimeoutError  # 使用Python内置的TimeoutError
# 所有域异常类
from .domains import *
# 异常处理器
from .handlers import (BaseExceptionHandler, BeanExceptionHandler,
                       ContextExceptionHandler, EventExceptionHandler,
                       ExceptionHandler, GlobalExceptionCoordinator,
                       RetryableExceptionHandler, ScheduleExceptionHandler,
                       SystemExceptionHandler, ValidationExceptionHandler,
                       register_default_handlers)
# 重试工具和装饰器
from .utils import (BackoffStrategy, ExceptionAction, ExponentialBackoff,
                    FixedBackoff, LinearBackoff, RandomJitterBackoff,
                    RetryStrategy, circuit_breaker, exception_handler,
                    exponential_backoff_retry, get_decorator_metrics,
                    handle_context_exceptions, performance_monitor, retry,
                    retry_with_backoff, timeout_handler, validate_arguments)

# 延迟初始化管理
_handlers_initialized = False

def ensure_handlers_initialized():
    """确保异常处理器已初始化（延迟初始化）"""
    global _handlers_initialized
    if not _handlers_initialized:
        register_default_handlers()
        _handlers_initialized = True

def reset_handlers():
    """重置异常处理器（主要用于测试）"""
    global _handlers_initialized
    from .handlers.base import GlobalExceptionCoordinator
    coordinator = GlobalExceptionCoordinator()
    coordinator.clear_handlers()
    _handlers_initialized = False

# 注意：异常处理器现在采用延迟初始化
# 在需要时会自动调用 ensure_handlers_initialized()

__all__ = [
    # 基础异常类
    "MiniBootError",
    "MiniBootException",
    "SystemError",
    "ApplicationError",
    "BusinessError",
    "ValidationError",
    "TimeoutError",

    # Bean相关异常
    "BeanCreationError", "BeanInitializationError", "BeanDestructionError",
    "BeanNotFoundError", "MultipleBeanFoundError", "BeanCurrentlyInCreationError",
    "BeanCircularDependencyError", "UnsatisfiedDependencyError", "DependencyInjectionError",
    "BeanTypeMismatchError", "BeanNotOfRequiredTypeError", "BeanInstantiationError",
    "BeanDefinitionError", "BeanDefinitionValidationError", "BeanDefinitionStoreError", "BeanDefinitionOverrideError",
    "BeanFactoryError", "BeanFactoryNotInitializedError", "BeanPostProcessorError",
    "BeanScopeError", "InvalidBeanScopeError",
    "BeanPropertyError", "BeanPropertyAccessError",
    "BeanMethodInvocationError", "BeanMethodNotFoundError",
    "BeanConfigurationError", "BeanConfigurationPropertiesError",
    "BeanLifecycleError", "BeanDisposalError",

    # Context相关异常
    "ContextStartupError", "ContextInitializationError", "ContextBootstrapError",
    "ContextShutdownError", "ContextDestructionError", "ContextCleanupError",
    "ContextConfigurationError", "ContextConfigurationValidationError", "ContextPropertiesError",
    "ContextStateError", "ContextNotStartedError", "ContextAlreadyStartedError", "ContextNotActiveError", "ContextClosedError",
    "ContextRefreshError", "ContextReloadError",
    "ContextHierarchyError", "ParentContextError", "ChildContextError",
    "ContextEventError", "ContextEventPublishError", "ContextEventHandlerError",
    "ContextResourceError", "ContextResourceLoadError", "ContextResourceNotFoundError",
    "ContextEnvironmentError", "ContextProfileError", "ContextPropertySourceError",
    "ContextProcessorError", "ContextPostProcessorError",
    "ContextSecurityError", "ContextPermissionError",
    "ContextMonitoringError", "ContextHealthCheckError",

    # Event相关异常
    "EventPublishError", "EventPublisherError", "EventDispatchError", "EventBroadcastError",
    "EventHandlerError", "EventHandlerNotFoundError", "EventHandlerRegistrationError", "EventHandlerExecutionError",
    "EventListenerError", "EventListenerRegistrationError", "EventListenerExecutionError",
    "EventTypeError", "InvalidEventTypeError", "EventSerializationError", "EventDeserializationError",
    "EventQueueError", "EventQueueFullError", "EventQueueEmptyError", "EventQueueTimeoutError",
    "EventBusError", "EventBusNotStartedError", "EventBusConfigurationError",
    "EventFilterError", "EventFilterExecutionError",
    "EventTransformError", "EventTransformerError",
    "EventRoutingError", "EventRouterError", "EventRouteNotFoundError",
    "EventStoreError", "EventPersistenceError", "EventRetrievalError",
    "EventSynchronizationError", "EventOrderingError",
    "EventSecurityError", "EventPermissionError",
    "EventMonitoringError", "EventMetricsError",

    # Schedule相关异常
    "TaskExecutionError", "TaskTimeoutError", "TaskInterruptedError", "TaskFailureError",
    "TaskSchedulingError", "SchedulerNotStartedError", "SchedulerAlreadyStartedError", "SchedulerShutdownError", "SchedulerConfigurationError",
    "TaskDefinitionError", "InvalidTaskDefinitionError", "TaskValidationError",
    "TaskStateError", "TaskNotFoundError", "TaskAlreadyExistsError", "TaskNotScheduledError", "TaskAlreadyScheduledError",
    "TriggerError", "InvalidTriggerError", "TriggerMisfireError", "CronExpressionError",
    "TaskQueueError", "TaskQueueFullError", "TaskQueueEmptyError",
    "ThreadPoolError", "ThreadPoolExhaustionError", "ThreadPoolShutdownError",
    "TaskPersistenceError", "TaskStoreError", "TaskRetrievalError",
    "TaskMonitoringError", "TaskMetricsError", "TaskHistoryError",
    "TaskDependencyError", "TaskDependencyNotMetError", "CircularTaskDependencyError",
    "TaskLockError", "TaskLockAcquisitionError", "TaskLockTimeoutError",
    "TaskSecurityError", "TaskPermissionError",
    "TaskResourceError", "TaskResourceExhaustionError",

    # Processor相关异常
    "ProcessorExecutionError", "ProcessorTimeoutError", "ProcessorInterruptedError",
    "ProcessorConfigurationError", "ProcessorInitializationError", "ProcessorRegistrationError",
    "ProcessorStateError", "ProcessorNotFoundError", "ProcessorAlreadyExistsError",
    "ProcessorChainError", "ProcessorChainExecutionError", "ProcessorChainInterruptedError",
    "ProcessorValidationError", "InvalidProcessorError",
    "ProcessorResourceError", "ProcessorResourceExhaustionError",

    # Resource相关异常
    "ResourceError", "ResourceNotFoundError", "ResourceLoadError", "PropertySourceLoadError",
    "ResourceAccessError", "ResourcePermissionError",
    "ResourceFormatError", "ResourceParseError",
    "ResourceCacheError", "ResourceCacheExpiredError",

    # AutoConfigure相关异常
    "AutoConfigurationError", "AutoConfigurationFailureError", "AutoConfigurationTimeoutError",
    "ConfigurationClassError", "InvalidConfigurationClassError", "ConfigurationClassNotFoundError",
    "ConditionalConfigurationError", "ConditionEvaluationError", "ConditionalBeanError",
    "ConfigurationPropertiesError", "ConfigurationPropertiesBindingError", "ConfigurationPropertiesValidationError",
    "ConfigurationImportError", "ImportSelectorError", "ImportRegistrarError",
    "ComponentScanError", "ClassPathScanningError", "AnnotationScanningError",

    # 重试工具
    "retry",
    "ExceptionAction",
    "RetryStrategy",
    "BackoffStrategy",
    "FixedBackoff",
    "ExponentialBackoff",
    "LinearBackoff",
    "RandomJitterBackoff",


    # 异常处理装饰器
    "exception_handler",
    "handle_context_exceptions",
    "timeout_handler",
    "performance_monitor",
    "circuit_breaker",
    "retry_with_backoff",
    "exponential_backoff_retry",
    "validate_arguments",
    "get_decorator_metrics",



    # 异常处理器
    "ExceptionHandler",
    "BaseExceptionHandler",
    "RetryableExceptionHandler",
    "GlobalExceptionCoordinator",
    "BeanExceptionHandler",
    "ContextExceptionHandler",
    "EventExceptionHandler",
    "ScheduleExceptionHandler",
    "SystemExceptionHandler",
    "ValidationExceptionHandler",
    "register_default_handlers",
    "ensure_handlers_initialized",
    "reset_handlers",
]
