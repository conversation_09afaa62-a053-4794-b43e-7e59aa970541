#!/usr/bin/env python
"""
* @author: cz
* @description: 字符串类型转换器实现
"""

from typing import Any

from miniboot.errors import ConversionError
from .base import Converter


class StrToBoolConverter(Converter[bool]):
    """字符串到布尔值转换器"""

    TRUE_VALUES = {"true", "yes", "1", "on", "enabled"}
    FALSE_VALUES = {"false", "no", "0", "off", "disabled"}

    def can_convert(self, source_type: type, target_type: type) -> bool:
        return source_type is str and target_type is bool

    def convert(self, source: Any, _target_type: type[bool]) -> bool:
        if not isinstance(source, str):
            raise ConversionError(f"Expected string, got {type(source).__name__}")

        value = source.lower().strip()
        if value in self.TRUE_VALUES:
            return True
        elif value in self.FALSE_VALUES:
            return False
        else:
            raise ConversionError(f"Cannot convert '{source}' to boolean")


class StrToIntConverter(Converter[int]):
    """字符串到整数转换器"""

    def can_convert(self, source_type: type, target_type: type) -> bool:
        return source_type is str and target_type is int

    def convert(self, source: Any, _target_type: type[int]) -> int:
        if not isinstance(source, str):
            raise ConversionError(f"Expected string, got {type(source).__name__}")

        try:
            # 处理不同进制的数字
            value = source.strip()
            if value.startswith(("0x", "0X")):
                # 十六进制
                return int(value, 16)
            elif value.startswith(("0b", "0B")):
                # 二进制
                return int(value, 2)
            elif value.startswith(("0o", "0O")):
                # 八进制
                return int(value, 8)
            else:
                # 十进制
                return int(value)
        except ValueError as e:
            raise ConversionError(f"Cannot convert '{source}' to integer: {e}") from e


class StrToFloatConverter(Converter[float]):
    """字符串到浮点数转换器"""

    def can_convert(self, source_type: type, target_type: type) -> bool:
        return source_type is str and target_type is float

    def convert(self, source: Any, _target_type: type[float]) -> float:
        if not isinstance(source, str):
            raise ConversionError(f"Expected string, got {type(source).__name__}")

        try:
            return float(source.strip())
        except ValueError as e:
            raise ConversionError(f"Cannot convert '{source}' to float: {e}") from e


class StrToListConverter(Converter[list]):
    """字符串到列表转换器"""

    def can_convert(self, source_type: type, target_type: type) -> bool:
        return source_type is str and target_type is list

    def convert(self, source: Any, _target_type: type[list]) -> list:
        if not isinstance(source, str):
            raise ConversionError(f"Expected string, got {type(source).__name__}")

        # 处理空字符串
        value = source.strip()
        if not value:
            return []

        # 按逗号分割并去除空格
        items = [item.strip() for item in value.split(",")]
        return [item for item in items if item]  # 过滤空字符串


class IntToFloatConverter(Converter[float]):
    """整数到浮点数转换器"""

    def can_convert(self, source_type: type, target_type: type) -> bool:
        return source_type is int and target_type is float

    def convert(self, source: Any, _target_type: type[float]) -> float:
        if not isinstance(source, int):
            raise ConversionError(f"Expected int, got {type(source).__name__}")

        return float(source)


class FloatToStringConverter(Converter[str]):
    """浮点数到字符串转换器"""

    def can_convert(self, source_type: type, target_type: type) -> bool:
        return source_type is float and target_type is str

    def convert(self, source: Any, _target_type: type[str]) -> str:
        if not isinstance(source, (int, float)):
            raise ConversionError(f"Expected float, got {type(source).__name__}")

        return str(source)


class ObjectToStringConverter(Converter[str]):
    """对象到字符串转换器"""

    def can_convert(self, _source_type: type, target_type: type) -> bool:
        return target_type is str

    def convert(self, source: Any, _target_type: type[str]) -> str:
        return str(source)
